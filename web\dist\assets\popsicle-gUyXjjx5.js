import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.1511 2.95015C13.8848 0.216485 18.317 0.216485 21.0506 2.95015C23.7843 5.68382 23.7843 10.116 21.0506 12.8496L12.2118 21.6885L9.38338 18.8601L6.2014 22.042C5.02983 23.2136 3.13033 23.2136 1.95876 22.042C0.787185 20.8705 0.787185 18.971 1.95876 17.7994L5.14074 14.6174L2.31231 11.789L11.1511 2.95015ZM12.2118 18.8601L19.2535 11.8183C19.1761 11.5497 19.0433 11.1933 18.8215 10.8209C18.3707 10.064 17.5348 9.20233 15.9072 8.88096C13.6457 8.43445 12.3603 7.17485 11.662 6.00232C11.5652 5.8398 11.4801 5.67972 11.4052 5.52457L5.14074 11.789L12.2118 18.8601ZM12.957 4.01145C13.0356 4.27634 13.1667 4.62018 13.3803 4.97894C13.8311 5.73576 14.667 6.59748 16.2946 6.91884C18.556 7.36535 19.8415 8.62495 20.5398 9.79749C20.5772 9.86034 20.6129 9.92284 20.647 9.98482C21.4931 8.13914 21.1563 5.88424 19.6364 4.36437C17.8095 2.53748 14.9207 2.41984 12.957 4.01145ZM6.55495 16.0316L3.37297 19.2136C2.98245 19.6041 2.98245 20.2373 3.37297 20.6278C3.7635 21.0184 4.39666 21.0184 4.78718 20.6278L7.96917 17.4458L6.55495 16.0316Z"}}]},g=f({name:"PopsicleIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-popsicle",o.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(m,C.value)}});export{g as default};
