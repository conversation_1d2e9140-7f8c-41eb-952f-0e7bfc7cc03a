import{d as O,h as a,ab as y,ac as d,ad as C}from"./index-CmSBegZC.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){C(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2.00002 4.99997C2.00004 3.34313 3.34318 2 5.00002 2H20V4H5.00002C4.44774 4 4.00003 4.44771 4.00002 4.99999C4.00001 5.55228 4.44773 6 5.00002 6H19C20.6568 6 22 7.34314 22 9V19C22 20.6569 20.6568 22 19 22H2L2.00002 4.99997V4.99997ZM4 7.82929V20H19C19.5522 20 20 19.5523 20 19V9C20 8.44772 19.5522 8 19 8H5.00002C4.64938 8 4.31278 7.93984 4 7.82929ZM15 13H18V15H15V13Z"}}]},g=O({name:"WalletIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=y(r),p=a(()=>["t-icon","t-icon-wallet",l.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>d(m,v.value)}});export{g as default};
