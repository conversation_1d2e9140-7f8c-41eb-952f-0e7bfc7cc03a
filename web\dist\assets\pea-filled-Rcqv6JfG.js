import{d as C,h as a,ab as d,ac as O,ad as y}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22.8741 3.00781L23.5355 4.84438L20.9999 6.45228V7.99987C20.9999 11.1459 20.4592 13.8298 19.201 15.9541C17.9268 18.1054 15.9693 19.5937 13.3229 20.4198C12.4101 20.7048 10.9 20.982 9.89117 20.9984C6.31921 21.0564 3.29501 19.4502 1.74315 17.6535L0.985352 16.7762L6.40425 13.3398C6.3927 13.3346 6.38117 13.3293 6.36966 13.324C5.48554 12.9171 4.61207 12.3218 3.84186 11.6379C3.07165 10.9541 2.37713 10.1573 1.86837 9.32754C1.3678 8.51116 0.999926 7.58535 0.999926 6.66295V5.78176L22.8741 3.00781ZM13.2321 11.3782L10.4032 13.1722L11.8601 15.3377L10.2008 16.4541L8.71354 14.2436L4.12018 17.1565C5.43722 18.1825 7.4778 19.0373 9.85868 18.9987C10.6452 18.9859 11.9643 18.7487 12.727 18.5107C14.9556 17.815 16.4856 16.6141 17.4801 14.9349C18.4907 13.2288 18.9999 10.9409 18.9999 7.99987V7.72057L14.9211 10.3072L16.3716 12.5787L14.6859 13.6551L13.2321 11.3782Z"}}]},b=C({name:"PeaFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-pea-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(L,f.value)}});export{b as default};
