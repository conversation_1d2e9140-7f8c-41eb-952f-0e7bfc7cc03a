<template>
  <CmpContainer full>
    <CmpCard>
      <t-form label-align="top" label-width="100px" class="form-container form-container-center">
        <div class="form-item" style="padding: 24px">
          <!-- <div class="form-container-title">基础信息</div>
          <t-row :gutter="[32, 24]">
            <t-col :span="6">
              <t-form-item label="动作名称">
                <t-input disabled placeholder="请输入动作名称" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="版本">
                <t-input disabled placeholder="请输入版本" />
              </t-form-item>
            </t-col>
          </t-row> -->

          <div class="form-container-title">局部变量</div>
          <variable-list v-model:data="flowInfo.data" open-customize-disabled :show-root-node="false"></variable-list>
        </div>
      </t-form>
    </CmpCard>
  </CmpContainer>
</template>
<script lang="ts">
export default {
  name: 'ActionInfo',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { watch } from 'vue';

import VariableList from '@/components/action-panel/VariableList.vue';

import { useActionFlowStore } from './store/index';

const actionFlowStore = useActionFlowStore();

const { flowInfo } = storeToRefs(actionFlowStore);

watch(
  () => flowInfo.value.data,
  (newValue) => {
    actionFlowStore.setFlowData(newValue);
  },
  {
    deep: true,
  },
);
</script>
<style lang="less" scoped>
@import '@/style/form.less';

.form-item {
  width: 800px;
}
</style>
