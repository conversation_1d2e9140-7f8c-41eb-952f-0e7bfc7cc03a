import{d as v,h as a,ab as d,ac as O,ad as y}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 1V2H16V8H15.0614C15.2612 9.7854 15.9348 12.1988 16.8796 14.5814C17.9352 17.2434 19.2498 19.6653 20.4322 21H22V23H14.1527L14.0144 22.1689L14.0121 22.157C14.0097 22.1443 14.0052 22.1221 13.9983 22.0915C13.9844 22.0303 13.9613 21.9373 13.9265 21.8233C13.8558 21.5918 13.7427 21.2933 13.5739 21.0038C13.2377 20.4274 12.7631 20 12 20C11.2369 20 10.7623 20.4274 10.4261 21.0038C10.2573 21.2933 10.1442 21.5918 10.0735 21.8233C10.0387 21.9373 10.0156 22.0303 10.0017 22.0915C9.99484 22.1221 9.99033 22.1443 9.98787 22.157L9.98579 22.168L9.84729 23H2V21H3.56781C4.75023 19.6653 6.0648 17.2434 7.12042 14.5814C8.06522 12.1988 8.73883 9.7854 8.93861 8H8V2H11V1H13ZM10.9489 8C10.7483 10.1007 9.977 12.8034 8.97958 15.3186C8.16311 17.3775 7.14961 19.4261 6.08129 21H8.23905C8.34209 20.7056 8.49 20.3536 8.69847 19.9962C9.23712 19.0726 10.2625 18 12 18C13.7375 18 14.7629 19.0726 15.3015 19.9962C15.51 20.3536 15.6579 20.7056 15.7609 21H17.9187C16.8504 19.4261 15.8369 17.3775 15.0204 15.3186C14.023 12.8034 13.2517 10.1007 13.0511 8H10.9489Z"}}]},g=v({name:"Tower1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-tower-1-filled",l.value]),C=a(()=>s(s({},c.value),r.style)),u=a(()=>({class:p.value,style:C.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(m,u.value)}});export{g as default};
