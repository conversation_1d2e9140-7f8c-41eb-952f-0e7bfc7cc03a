import{_ as u,f as l,c as d,o as p,g as o,a,t,n,p as c}from"./index-CmSBegZC.js";const f={name:"ResultFail"},_={class:"result-success"},m={class:"result-success-title"},b={class:"result-success-describe"};function g(s,e,v,$,h,k){const i=l("t-icon"),r=l("t-button");return p(),d("div",_,[o(i,{class:"result-success-icon",name:"error-circle"}),a("div",m,t(s.t("pages.result.fail.title")),1),a("div",b,t(s.t("pages.result.fail.subtitle")),1),a("div",null,[o(r,{theme:"default",onClick:e[0]||(e[0]=()=>s.$router.push("/dashboard/base"))},{default:n(()=>[c(t(s.t("pages.result.fail.back")),1)]),_:1}),o(r,{onClick:e[1]||(e[1]=()=>s.$router.push("/form/base"))},{default:n(()=>[c(t(s.t("pages.result.fail.modify")),1)]),_:1})])])}const B=u(f,[["render",g],["__scopeId","data-v-cd4e7bb9"]]);export{B as default};
