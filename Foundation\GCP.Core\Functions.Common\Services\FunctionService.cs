using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Builder;
using GCP.Functions.Common.Models;
using GCP.FunctionPool;

namespace GCP.Functions.Common.Services
{
    [Function("function", "函数服务")]
    internal class FunctionService : BaseService
    {
        [Function("getAll", "获取函数清单")]
        public List<LcFunction> GetAll(string id = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcFunctions
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(id) || a.Id == id)
                        select a).ToList();
            return data;
        }

        [Function("get", "获取函数信息")]
        public LcFunction Get(string id)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcFunctions
                        where a.Id == id &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        select a).FirstOrDefault();
            return data;
        }

        [Function("getByIds", "获取函数信息")]
        public List<OptionVO> GetByIds(string[] ids)
        {
            return FunctionCompiler.DicFunction.Where(t => ids.Contains(t.Key))
                .Select(t => new OptionVO { Value = t.Key, Label = t.Value.FunctionName }).ToList();
        }

        [Function("getVersion", "获取函数版本")]
        public long? GetVersion(string id)
        {
            return Get(id)?.UseVersion;
        }

        /// <summary>
        /// 测试执行函数
        /// </summary>
        /// <param name="functionId">函数ID</param>
        /// <param name="parameters">测试参数</param>
        /// <param name="triggerType">触发类型：API、JOB、EVENT、TEST等，默认为TEST</param>
        /// <returns>执行结果，包含processId用于查看日志</returns>
        [Function("test", "测试执行函数")]
        public async Task<object> TestAsync(string functionId, Dictionary<string, object> parameters = null, string triggerType = "TEST")
        {
            if (string.IsNullOrWhiteSpace(functionId))
            {
                throw new CustomException("函数ID不能为空");
            }

            // 获取函数信息
            var function = Get(functionId);
            if (function == null)
            {
                throw new CustomException($"函数不存在: {functionId}");
            }

            // 根据触发类型创建执行配置
            FunctionExecutionConfig config;
            switch (triggerType?.ToUpper())
            {
                case "API":
                    config = FunctionExecutionConfigBuilder.ForApi(
                        this.SolutionId,
                        this.ProjectId,
                        functionId,
                        function.FunctionName,
                        parameters,
                        version: function.UseVersion
                    );
                    break;
                case "JOB":
                    config = FunctionExecutionConfigBuilder.ForJob(
                        this.SolutionId,
                        this.ProjectId,
                        functionId,
                        function.FunctionName,
                        "test", // creator
                        parameters,
                        version: function.UseVersion
                    );
                    break;
                case "EVENT":
                    config = FunctionExecutionConfigBuilder.ForEvent(
                        this.SolutionId,
                        this.ProjectId,
                        functionId,
                        function.FunctionName,
                        parameters,
                        version: function.UseVersion
                    );
                    break;
                case "TEST":
                default:
                    throw new CustomException("触发类型不支持");
            }

            try
            {
                // 使用 FunctionExecutionHelper.ExecuteWithLogging 执行函数
                var result = await FunctionExecutionHelper.ExecuteWithLogging(config, async (context) =>
                {
                    // 执行函数
                    return await FunctionHelper.Runner.Execute(new FunctionInvokeDTO
                    {
                        path = functionId,
                        args = parameters ?? new Dictionary<string, object>()
                    }, (ctx) =>
                    {
                        ctx.Persistence = true; // 启用步骤日志
                        ctx.trackId = context.trackId;
                        if (ctx.Middlewares != null)
                            ctx.Middlewares.Add(TimeMiddleware.Handler);
                        return Task.CompletedTask;
                    });
                });

                return new
                {
                    success = true,
                    result = result,
                    processId = config.TrackId,
                    message = "函数执行成功"
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    success = false,
                    result = (string)null,
                    processId = config.TrackId,
                    message = $"函数执行失败: {ex.Message}",
                    error = ex.ToString()
                };
            }
        }

    }
}
