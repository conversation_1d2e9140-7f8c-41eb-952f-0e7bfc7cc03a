import{d as v,h as n,ab as O,ac as y,ad as g}from"./index-CmSBegZC.js";function i(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?i(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16 1H8V5H16V1Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M6 3H3V23H12.75V11H21V3H18V7H6V3Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M14.75 12.9961H23.25V23.2906L19.0029 20.6742L14.75 23.2886V12.9961Z"}}]},h=v({name:"TaskMarkedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,a){var{attrs:t}=a,r=n(()=>e.size),{className:l,style:c}=O(r),p=n(()=>["t-icon","t-icon-task-marked-filled",l.value]),u=n(()=>s(s({},c.value),t.style)),f=n(()=>({class:p.value,style:u.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>y(m,f.value)}});export{h as default};
