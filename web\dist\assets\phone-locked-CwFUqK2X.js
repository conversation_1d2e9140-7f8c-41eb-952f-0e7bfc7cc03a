import{d,h as a,ab as O,ac as y,ad as h}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){h(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 1H20V11H18V3H6V21H11V23H4V1ZM17.5 14.5C18.1904 14.5 18.75 15.0596 18.75 15.75V16.5H16.25V15.75C16.25 15.0596 16.8096 14.5 17.5 14.5ZM20.75 16.5V15.75C20.75 13.9551 19.2949 12.5 17.5 12.5C15.7051 12.5 14.25 13.9551 14.25 15.75V16.5H12.9985V23H21.9985V16.5H20.75ZM19.9985 18.5V21H14.9985V18.5H19.9985Z"}}]},b=d({name:"PhoneLockedIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=O(t),p=a(()=>["t-icon","t-icon-phone-locked",o.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(m,v.value)}});export{b as default};
