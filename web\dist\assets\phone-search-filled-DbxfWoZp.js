import{d as C,h as a,ab as d,ac as h,ad as O}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M20 1H4V23H14.7964C12.5643 22.0463 11 19.8308 11 17.25C11 17.1663 11.0016 17.0829 11.0049 17H6V3H18V11.0445C18.7101 11.1295 19.3836 11.3334 20 11.6359V1Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M22 17.25C22 18.1992 21.7216 19.0834 21.2419 19.8254L22.9142 21.5017L21.4983 22.9142L19.8282 21.2401C19.0857 21.7209 18.2004 22 17.25 22C14.6266 22 12.5 19.8734 12.5 17.25C12.5 14.6266 14.6266 12.5 17.25 12.5C19.8734 12.5 22 14.6266 22 17.25ZM20 17.25C20 15.7312 18.7688 14.5 17.25 14.5C15.7312 14.5 14.5 15.7312 14.5 17.25C14.5 18.7688 15.7312 20 17.25 20C18.0059 20 18.6906 19.695 19.1876 19.2014L19.1967 19.1924C19.6931 18.6949 20 18.0083 20 17.25Z"}}]},m=C({name:"PhoneSearchFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=d(t),p=a(()=>["t-icon","t-icon-phone-search-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>h(y,f.value)}});export{m as default};
