import{d,h as a,ab as L,ac as C,ad as O}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15.844 3.34403L14.416 4.12503L15.844 4.90604L16.625 6.33405L17.406 4.90604L18.834 4.12503L17.406 3.34403L16.625 1.91602L15.844 3.34403ZM10.412 4.15764C6.75443 4.89424 4 8.12547 4 12C4 16.4183 7.58172 20 12 20C14.9602 20 17.5466 18.3918 18.9302 15.9998C13.9918 15.9623 10 11.9473 10 7.00003C10 6.02088 10.1313 5.06319 10.412 4.15764ZM2 12C2 6.47719 6.47715 2.00003 12 2.00003H13.7337L12.8656 3.50073C12.2871 4.50094 12 5.68851 12 7.00003C12 10.866 15.134 14 19 14C19.4618 14 19.9122 13.9555 20.3475 13.8707L22.0301 13.5428L21.4872 15.1689C20.1623 19.1373 16.4167 22 12 22C6.47715 22 2 17.5229 2 12ZM20.5 6.41602L21.4136 8.08645L23.084 9.00003L21.4136 9.91362L20.5 11.5841L19.5864 9.91362L17.916 9.00003L19.5864 8.08645L20.5 6.41602Z"}}]},b=d({name:"ModeDarkIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=L(t),p=a(()=>["t-icon","t-icon-mode-dark",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>C(y,v.value)}});export{b as default};
