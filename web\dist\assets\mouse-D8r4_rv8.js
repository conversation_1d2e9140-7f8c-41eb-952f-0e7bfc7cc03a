import{d as O,h as a,ab as y,ac as d,ad as m}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 9C4 4.58172 7.58172 1 12 1C16.4183 1 20 4.58172 20 9V15C20 19.4183 16.4183 23 12 23C7.58172 23 4 19.4183 4 15V9ZM11 3.08296C8.16229 3.55904 6 6.027 6 9H11V3.08296ZM13 3.08296V9H18C18 6.027 15.8377 3.55904 13 3.08296ZM18 11H6V15C6 18.3137 8.68629 21 12 21C15.3137 21 18 18.3137 18 15V11Z"}}]},g=O({name:"MouseIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-mouse",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>d(C,v.value)}});export{g as default};
