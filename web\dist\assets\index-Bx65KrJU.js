import{c as r,o as l,aS as i,d as n,h as d,a,L as p,b as c,R as u,t as s,_ as g}from"./index-CmSBegZC.js";const m={xmlns:"http://www.w3.org/2000/svg",width:"200",height:"140",fill:"none"};function v(t,e){return l(),r("svg",m,e[0]||(e[0]=[i('<g mask="url(#mask0_17_619)"><path fill="#97A3B7" d="M30 62h88v60H30z"></path><g filter="url(#filter0_f_17_619)"><path fill="#E3E6EB" d="M12 84h80v60H12z"></path></g><g filter="url(#filter1_f_17_619)"><path fill="#E3E6EB" d="M80 54h80v60H80z"></path></g><path fill="#fff" d="M46 105h32v2H46zM46 98h32v2H46zM46 88h16v2H46z"></path></g><path fill="currentcolor" d="M63 20h88v10H63z" opacity=".9"></path><mask id="a" width="88" height="50" x="63" y="30" maskUnits="userSpaceOnUse" style="mask-type:alpha;"><path fill="currentcolor" d="M63 30h88v50H63z"></path></mask><g mask="url(#a)"><path fill="currentcolor" d="M63 30h88v50H63z"></path><g filter="url(#filter2_f_17_619)" opacity=".3"><path fill="#97A3B7" d="M30 62h88v60H30z"></path></g></g><path fill="#fff" fill-rule="evenodd" d="m95.686 40.858 10.278 10.277A4 4 0 0 1 107 51c2.213 0 4 1.786 4 4q-.002.538-.134 1.033l10.276 10.28-2.828 2.829-4.598-4.597A17.1 17.1 0 0 1 107 65.909c-7.273 0-13.484-4.524-16-10.91a17.3 17.3 0 0 1 5.696-7.472l-3.838-3.84zM99 55a8.003 8.003 0 0 0 12.063 6.892l-3.029-3.026q-.496.132-1.034.134c-2.213 0-4-1.787-4-4q.002-.538.134-1.034l-3.027-3.027A7.96 7.96 0 0 0 99 54.999m8-10.91c7.273 0 13.484 4.524 16 10.91a17.3 17.3 0 0 1-4.166 6.177l-4.105-4.105a8.003 8.003 0 0 0-9.801-9.8l-2.55-2.551c1.47-.41 3.02-.63 4.622-.63" clip-rule="evenodd"></path><path fill="#fff" d="M68 24h2v2h-2zM74 24h2v2h-2zM80 24h66v2H80z"></path><path fill="#fff" stroke="#000" d="m157 54 24.249 42h-48.498z"></path><path stroke="#000" d="M157 89V71"></path>',8)]))}const f={render:v},_={xmlns:"http://www.w3.org/2000/svg",width:"200",height:"140",fill:"none"};function z(t,e){return l(),r("svg",_,e[0]||(e[0]=[i('<g mask="url(#mask0_16559_24301)"><path fill="#97A3B7" d="M30 62h88v60H30z"></path><g filter="url(#filter0_f_16559_24301)"><path fill="#E3E6EB" d="M12 84h80v60H12z"></path></g><g filter="url(#filter1_f_16559_24301)"><path fill="#E3E6EB" d="M80 54h80v60H80z"></path></g><path stroke="#fff" stroke-width="2" d="m49 93-7 7 7 7M69 107l7-7-7-7M62.365 87.443l-6.73 25.114"></path></g><path fill="currentcolor" d="M63 20h88v10H63z" opacity=".9"></path><mask id="a" width="88" height="50" x="63" y="30" maskUnits="userSpaceOnUse" style="mask-type:alpha;"><path fill="currentcolor" d="M63 30h88v50H63z"></path></mask><g mask="url(#a)"><path fill="currentcolor" d="M63 30h88v50H63z"></path><g filter="url(#filter2_f_16559_24301)" opacity=".3"><path fill="#97A3B7" d="M30 62h88v60H30z"></path></g></g><path fill="#fff" fill-rule="evenodd" d="M105.25 41c6.765 0 12.25 5.484 12.25 12.25 0 2.433-.709 4.7-1.932 6.606L121 65.288 117.288 69l-5.432-5.432a12.2 12.2 0 0 1-6.606 1.932C98.484 65.5 93 60.016 93 53.25S98.484 41 105.25 41m0 3.5a8.75 8.75 0 1 0 0 17.5 8.75 8.75 0 0 0 0-17.5" clip-rule="evenodd"></path><path fill="#fff" d="M68 24h2v2h-2zM74 24h2v2h-2zM80 24h66v2H80z"></path><path fill="#fff" fill-rule="evenodd" d="M153 56c-12.15 0-22 9.85-22 22 0 4.604 1.414 8.878 3.832 12.41L127 98.5l12.495-3.132A21.9 21.9 0 0 0 153 100c12.15 0 22-9.85 22-22s-9.85-22-22-22" clip-rule="evenodd"></path><path fill="#000" d="M131 78h.5zm3.832 12.41.359.348.284-.293-.23-.337zM127 98.5l-.359-.348-1.219 1.259 1.7-.426zm12.495-3.132.307-.394-.192-.15-.237.06zM153 100v.5zm22-22h-.5zm-43.5 0c0-11.874 9.626-21.5 21.5-21.5v-1c-12.426 0-22.5 10.074-22.5 22.5zm3.745 12.128A21.4 21.4 0 0 1 131.5 78h-1c0 4.708 1.446 9.08 3.919 12.693zm-.772-.066-7.832 8.09.718.696 7.832-8.09zm-7.351 8.923 12.494-3.132-.243-.97-12.495 3.132zM153 99.5c-4.976 0-9.555-1.69-13.198-4.526l-.615.789A22.4 22.4 0 0 0 153 100.5zM174.5 78c0 11.874-9.626 21.5-21.5 21.5v1c12.426 0 22.5-10.074 22.5-22.5zM153 56.5c11.874 0 21.5 9.626 21.5 21.5h1c0-12.426-10.074-22.5-22.5-22.5z"></path>',8)]))}const B={render:z},k={xmlns:"http://www.w3.org/2000/svg",width:"200",height:"140",fill:"none"};function M(t,e){return l(),r("svg",k,e[0]||(e[0]=[i('<g mask="url(#mask0_16559_24251)"><path fill="#97A3B7" d="m68 48 38.105 22v44L68 136l-38.105-22V70z"></path><g filter="url(#a)"><path fill="#E3E6EB" d="M46.391 92h80v60h-80z"></path></g><g filter="url(#b)"><path fill="#E3E6EB" d="M0 23h80v60H0z"></path></g></g><mask id="c" width="78" height="88" x="80" y="9" maskUnits="userSpaceOnUse" style="mask-type:alpha;"><path fill="currentcolor" d="m119 9 38.105 22v44L119 97 80.895 75V31z"></path></mask><g mask="url(#c)"><path fill="currentcolor" fill-rule="evenodd" d="M80.895 31v44L119 97l38.105-22V31L119 53z" clip-rule="evenodd"></path><path fill="currentcolor" d="m119-35 38.105 22v44.5L119 53.5l-38.105-22V-13z" opacity=".9"></path><g filter="url(#d)" opacity=".3"><path fill="#97A3B7" d="m68 48 38.105 22v44L68 136l-38.105-22V70z"></path></g></g><path fill="#fff" stroke="#000" d="m143 68.822 4.867 17.053.133.466.469-.118 17.202-4.312-12.335 12.741-.337.348.337.348 12.335 12.741-17.202-4.312-.469-.118-.133.466L143 121.178l-4.867-17.053-.133-.466-.469.118-17.202 4.312 12.335-12.741.337-.348-.337-.348-12.335-12.741 17.202 4.312.469.118.133-.466z"></path><path fill="#fff" fill-rule="evenodd" d="m123.243 35.082 2.828-1.633-2.828-1.633-2.829 1.633zM119 32.632 121.828 31l-7.071-4.082-2.828 1.633zm8.485 3.267c-4.679 2.701-12.291 2.701-16.97 0-4.68-2.702-4.68-7.096 0-9.798s12.291-2.701 16.97 0c4.68 2.702 4.68 7.096 0 9.798m-19.799-11.43c-6.248 3.607-6.248 9.455 0 13.062s16.38 3.607 22.628 0 6.248-9.455 0-13.062-16.38-3.607-22.628 0M41.899 86.286l2.828 1.633v6.532L41.9 92.818zm12.02 6.94 2.829 1.634v6.532l-2.828-1.633zm-9.19 14.289-2.83-1.633.001 3.266 2.828 1.633zl9.192 5.307v3.266l2.828 1.633v-3.266l-2.828-1.633v-3.266l-9.192-5.307z" clip-rule="evenodd"></path><defs><filter id="a" width="180" height="160" x="-3.609" y="42" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feGaussianBlur result="effect1_foregroundBlur_16559_24251" stdDeviation="25"></feGaussianBlur></filter><filter id="b" width="180" height="160" x="-50" y="-27" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feGaussianBlur result="effect1_foregroundBlur_16559_24251" stdDeviation="25"></feGaussianBlur></filter><filter id="d" width="88.21" height="100" x="23.895" y="42" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feGaussianBlur result="effect1_foregroundBlur_16559_24251" stdDeviation="3"></feGaussianBlur></filter></defs>',6)]))}const w={render:M},y={xmlns:"http://www.w3.org/2000/svg",width:"200",height:"140",fill:"none"};function x(t,e){return l(),r("svg",y,e[0]||(e[0]=[i('<g mask="url(#mask0_22_990)"><path fill="#97A3B7" fill-rule="evenodd" d="m144.569 105.61-48 27.712-48-27.712V83.712L96.57 56l48 27.713z" clip-rule="evenodd"></path><g filter="url(#filter0_f_22_990)"><path fill="#E3E6EB" d="M-3 33.999h80v60H-3z"></path></g><g filter="url(#filter1_f_22_990)"><path fill="#E3E6EB" d="M97 97.999h80v60H97z"></path></g></g><mask id="a" width="86" height="69" x="53" y="16" maskUnits="userSpaceOnUse" style="mask-type:alpha;"><path fill="currentcolor" fill-rule="evenodd" d="m113.357 42.715 16.472-9.51a36 36 0 0 0-3.186-2.043c-5.375-3.104-11.92-5.147-18.885-6.139-4.209-5.562-10.18-8.898-17.324-8.828-6.755.087-15.486 5.746-21.507 14.365q-1.247.622-2.432 1.306c-14.378 8.3-16.94 20.75-7.473 30.065-1.035-5.805-.31-10.865 3.101-16.215-.058.632-.21 4.427-.232 5.067-.67 18.914 15.02 34.058 26.139 33.928 8.45-.1 15.565-5.013 20.319-12.631 6.214-1.231 12.089-3.294 17.094-6.183 13.476-7.78 16.567-19.182 9.104-28.268l-16.599 9.582c1.762 3.654.049 7.823-5.078 10.783-5.126 2.96-12.347 3.948-18.677 2.931-2.843-.469-5.522-1.342-7.74-2.622l-.049-.028zM78.655 53.773c-6.127-3.977-5.683-10.226 1.195-14.197s17.703-4.226 24.591-.69zM93.556 18.17c4.61-.04 8.728 2.405 11.94 6.57-8.447-.918-17.384-.326-25.523 1.765 3.699-5.124 8.471-8.275 13.583-8.335m-2.232 63.447c-5.853.068-10.971-3.926-14.377-10.332 8.845 2.342 18.925 2.775 28.427 1.317-3.756 5.52-8.714 8.951-14.05 9.015" clip-rule="evenodd"></path></mask><g mask="url(#a)"><path fill="currentcolor" fill-rule="evenodd" d="m113.357 42.715 16.472-9.51a36 36 0 0 0-3.186-2.043c-5.375-3.104-11.92-5.147-18.885-6.139-4.209-5.562-10.18-8.898-17.324-8.828-6.755.087-15.486 5.746-21.507 14.365q-1.247.622-2.432 1.306c-14.378 8.3-16.94 20.75-7.473 30.065-1.035-5.805-.31-10.865 3.101-16.215-.058.632-.21 4.427-.232 5.067-.67 18.914 15.02 34.058 26.139 33.928 8.45-.1 15.565-5.013 20.319-12.631 6.214-1.231 12.089-3.294 17.094-6.183 13.476-7.78 16.567-19.182 9.104-28.268l-16.599 9.582c1.762 3.654.049 7.823-5.078 10.783-5.126 2.96-12.347 3.948-18.677 2.931-2.843-.469-5.522-1.342-7.74-2.622l-.049-.028zM78.655 53.773c-6.127-3.977-5.683-10.226 1.195-14.197s17.703-4.226 24.591-.69zM93.556 18.17c4.61-.04 8.728 2.405 11.94 6.57-8.447-.918-17.384-.326-25.523 1.765 3.699-5.124 8.471-8.275 13.583-8.335m-2.232 63.447c-5.853.068-10.971-3.926-14.377-10.332 8.845 2.342 18.925 2.775 28.427 1.317-3.756 5.52-8.714 8.951-14.05 9.015" clip-rule="evenodd"></path><g filter="url(#filter2_f_22_990)" opacity=".3"><path fill="#97A3B7" d="m96.569 56 48 27.712v55.426l-48 27.712-48-27.712V83.712z"></path></g></g><circle cx="155" cy="78" r="22" fill="#fff" stroke="#000" transform="rotate(180 155 78)"></circle><path stroke="#000" d="M155 83V65"></path><path fill="#C4C4C4" stroke="#000" stroke-linejoin="round" stroke-width="2" d="M155 87h.004v.004H155z"></path><path stroke="#fff" stroke-width="2" d="M96.57 112V88M86.57 98l10-10 9.999 10"></path>',7)]))}const E={render:x},H={xmlns:"http://www.w3.org/2000/svg",width:"200",height:"140",fill:"none"};function S(t,e){return l(),r("svg",H,e[0]||(e[0]=[i('<mask id="a" width="78" height="88" x="80" y="9" maskUnits="userSpaceOnUse" style="mask-type:alpha;"><path fill="currentColor" d="m119 9 38.105 22v44L119 97 80.895 75V31z"></path></mask><g mask="url(#a)"><path fill="currentColor" d="m119 9 38.105 22v44L119 97 80.895 75V31z"></path><g filter="url(#b)" opacity=".3"><path fill="#97A3B7" d="m68 48 38.105 22v44L68 136l-38.105-22V70z"></path></g></g><mask id="c" width="78" height="88" x="29" y="48" maskUnits="userSpaceOnUse" style="mask-type:alpha;"><path fill="#97A3B7" d="m68 48 38.105 22v44L68 136l-38.105-22V70z"></path></mask><g mask="url(#c)"><path fill="#97A3B7" d="m68 48 38.105 22v44L68 136l-38.105-22V70z"></path><g filter="url(#d)"><path fill="#E3E6EB" d="M46.391 92h80v60h-80z"></path></g><g filter="url(#e)"><path fill="#E3E6EB" d="M0 23h80v60H0z"></path></g></g><path fill="#fff" d="m41.898 86.287 2.829 1.633v6.531l-2.828-1.632zM53.919 93.227l2.828 1.633v6.532l-2.828-1.633zM44.728 107.515l-2.829-1.633v3.266l2.829 1.633zl9.191 5.308v3.265l2.829 1.633v-3.266l-2.828-1.632v-3.266l-9.193-5.308z"></path><path fill="#fff" fill-rule="evenodd" d="M108.348 23.48c-2.16 2.51-1.813 5.903 1.047 8.08 3.265 2.486 8.568 2.447 11.845-.087 3.276-2.534 3.286-6.603.021-9.089-2.86-2.177-7.284-2.417-10.54-.74l5.202 3.96-2.373 1.836z" clip-rule="evenodd"></path><path fill="#fff" fill-rule="evenodd" d="m120.865 33.009 8.965 6.826 2.373-1.836-8.965-6.826q-.481.523-1.089.993-.608.468-1.284.843m-1.203-.916a8.5 8.5 0 0 0 1.305-.828 7.248 7.248 0 0 1 0 0 8.5 8.5 0 0 1-1.305.828" clip-rule="evenodd"></path><path fill="#fff" stroke="#181818" d="m144 70 24.249 42h-48.498z"></path><path stroke="#181818" d="M144 100V82"></path><path stroke="#181818" stroke-linejoin="round" stroke-width="2" d="M144 105h.004v.004H144z"></path><defs><filter id="b" width="88.21" height="100" x="23.895" y="42" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feGaussianBlur result="effect1_foregroundBlur_216_313" stdDeviation="3"></feGaussianBlur></filter><filter id="d" width="180" height="160" x="-3.609" y="42" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feGaussianBlur result="effect1_foregroundBlur_216_313" stdDeviation="25"></feGaussianBlur></filter><filter id="e" width="180" height="160" x="-50" y="-27" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feGaussianBlur result="effect1_foregroundBlur_216_313" stdDeviation="25"></feGaussianBlur></filter></defs>',11)]))}const U={render:S},F={xmlns:"http://www.w3.org/2000/svg",width:"200",height:"140",fill:"none"};function G(t,e){return l(),r("svg",F,e[0]||(e[0]=[i('<g mask="url(#mask0_21_716)"><path fill="#97A3B7" fill-rule="evenodd" d="M33 46.843 96.321 119l63.322-72.157C142.742 32 120.583 23 96.321 23 72.061 23 49.901 32 33 46.843" clip-rule="evenodd"></path><g filter="url(#filter0_f_21_716)"><path fill="#E3E6EB" d="M95 21h80v60H95z"></path></g><g filter="url(#filter1_f_21_716)"><path fill="#E3E6EB" d="M-7 43h80v60H-7z"></path></g></g><path fill="currentcolor" d="m72.812 63.688-3.157 3.157 6.246 6.247a50.3 50.3 0 0 0-12.713 8.151L96.321 119l11.913-13.575 6.413 6.412 3.157-3.157L80.45 71.326zM129.455 81.243l-15.318 17.455-28.74-28.74a50.4 50.4 0 0 1 10.924-1.19c12.695 0 24.29 4.709 33.134 12.475"></path><path fill="#fff" stroke="#000" d="m152 21.822 4.867 17.053.133.465.469-.117 17.202-4.312-12.335 12.741-.337.348.337.348 12.335 12.741-17.202-4.312-.469-.117-.133.465L152 74.178l-4.867-17.053-.133-.465-.469.117-17.202 4.312 12.335-12.741.337-.348-.337-.348-12.335-12.741 17.202 4.312.469.117.133-.465z"></path><path stroke="#fff" stroke-width="2" d="M101 31 90 42l11 11-8 8"></path>',4)]))}const I={render:G},L={class:"result-container"},A={class:"result-bg-img"},V={class:"result-title"},R={class:"result-tip"},O=n({__name:"index",props:{bgUrl:String,title:String,tip:String,type:String},setup(t){const e=t,o=d(()=>{switch(e.type){case"403":return f;case"404":return B;case"500":return w;case"ie":return E;case"wifi":return I;case"maintenance":return U;default:return f}});return(h,$)=>(l(),r("div",L,[a("div",A,[(l(),c(u(o.value)))]),a("div",V,s(t.title),1),a("div",R,s(t.tip),1),p(h.$slots,"default",{},void 0,!0)]))}}),b=g(O,[["__scopeId","data-v-b107185c"]]);export{b as R};
