<template>
  <t-dialog
    v-model:visible="visible"
    attach="body"
    :header="'详情 > ' + processName"
    width="80%"
    top="72px"
    :footer="false"
  >
    <logs-panel :process-id="processId" :process-name="processName" :is-run="isRun" />
  </t-dialog>
</template>

<script lang="ts">
export default {
  name: 'SingleLogsDialog',
};
</script>

<script setup lang="ts">
import LogsPanel from '@/components/action-panel/LogsPanel.vue';

const props = defineProps<{
  processId: string;
  processName: string;
  isRun: boolean;
}>();

const visible = defineModel<boolean>('visible', { default: false });
</script>
