import{d as O,h as a,ab as d,ac as y,ad as C}from"./index-CmSBegZC.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){C(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17 1H7V3H3V23H10V21H5V5H7V7H17V5H19V13H21V3H17V1zM15 5H9V3H15V5zM15.75 20V18H18.25V20H15.75z"}},{tag:"path",attrs:{fill:"currentColor",d:"M17.0021 23.4998C21.4205 23.4998 23.0921 18.9998 23.0921 18.9998C23.0921 18.9998 21.4185 14.4998 17.0021 14.4998C12.5857 14.4998 10.9121 18.9998 10.9121 18.9998C10.9121 18.9998 12.5837 23.4998 17.0021 23.4998ZM17.0004 21.4998C14.3848 21.4998 13.1309 18.9998 13.1309 18.9998C13.1309 18.9998 14.3797 16.4998 17.0004 16.4998C19.6211 16.4998 20.8709 18.9998 20.8709 18.9998C20.8709 18.9998 19.616 21.4998 17.0004 21.4998Z"}}]},g=O({name:"TaskVisibleIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:i,style:c}=d(r),p=a(()=>["t-icon","t-icon-task-visible",i.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(b,v.value)}});export{g as default};
