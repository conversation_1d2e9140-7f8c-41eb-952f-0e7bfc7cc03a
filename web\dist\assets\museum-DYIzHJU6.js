import{d as m,h as a,ab as O,ac as y,ad as d}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 2H18V6.32718L22 5.88274V22H2V8.10496L6 7.66051V2ZM8 7.43829L16 6.5494V4H8V7.43829ZM18 20H20V15H18V20ZM20 13V8.11726L4 9.89504V20H16V13H20ZM6 10.998H8.00391V13.002H6V10.998ZM10 10.998H12.0039V13.002H10V10.998Z"}}]},g=m({name:"MuseumIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),u=a(()=>["t-icon","t-icon-museum",o.value]),p=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:u.value,style:p.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(V,v.value)}});export{g as default};
