import{d as L,h as a,ab as O,ac as y,ad as d}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17.1548 1.37402L22.6264 6.84564L21.2122 8.25985L15.7406 2.78824L17.1548 1.37402ZM14.3529 4.20673L19.794 9.64787L7.39722 22.001L2 22.001L2.00001 16.6038L14.3529 4.20673ZM14.3554 7.03769L4 17.4301L4 20.001L6.57086 20.001L16.9631 9.64537L14.3554 7.03769ZM22.2078 12.8662L15.9642 19.1098L14.55 17.6956L20.7936 11.452L22.2078 12.8662Z"}}]},g=L({name:"PenBallIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-pen-ball",l.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>y(b,v.value)}});export{g as default};
