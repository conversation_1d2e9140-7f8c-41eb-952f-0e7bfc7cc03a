﻿using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Serilog;
using System.Diagnostics;
using GCP.FunctionPool.Flow;
using EasyCaching.Core;
using GCP.Functions.Common.Services;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class FunctionHttpHandler
    {
        internal static Dictionary<string, ApiInfoDTO> ApiDic { get; set; } = [];
        internal static Dictionary<string, ApiInfoDTO> ForwarderDic { get; set; } = [];
        static IEasyCachingProvider _cache;

        internal static void LoadDbApi(string funcId = null)
        {
            var service = new ApiService();
            var data = service.GetListByFuncId(funcId);

            foreach (var item in data)
            {
                string key = "";
                Dictionary<string, ApiInfoDTO> dic = null;
                if (item.ApiType == 3)
                {
                    key = item.HttpUrl.ToLower();
                    dic = ForwarderDic;
                }
                else if (item.ApiType == 1)
                {
                    key = item.RequestType + " " + item.HttpUrl.ToLower();
                    dic = ApiDic;
                }

                if (dic == null) continue;
                var isDisabled = item.State == 0;

                if (isDisabled)
                {
                    dic?.Remove(key);
                }
                else
                {
                    if (dic.TryGetValue(key, out var itemDto))
                    {
                        if (funcId == null)
                        {
                            itemDto.Basice = item;
                        }
                        else
                        {
                            dic[key] = new ApiInfoDTO
                            {
                                Basice = item,
                            };
                        }
                    }
                    else
                    {
                        dic.Add(key, new ApiInfoDTO
                        {
                            Basice = item,
                        });
                    }
                }
            }
        }

        public static IApplicationBuilder UseFunctionPool(this IApplicationBuilder builder)
        {
            FunctionHelper.Runner = new FunctionRunner();
            FunctionHelper.TriggerApi = RunApiAsync;

            Task.Run(async () =>
            {
                _cache = ServiceLocator.Current.GetRequiredService<IEasyCachingProvider>();

                await LicenseManager.LoadLicense();
                LicenseSevice.CheckLicense = () => LicenseManager.IsActivated == true;

                FunctionRunner.InitRun();

                //foreach (var item in FunctionCompiler.dicFunction)
                //{
                //    if (item.Key.Contains('/'))
                //    {
                //        var key = "POST" + " " + item.Key;
                //        apiDic[key] = item.Key;
                //    }
                //}

                //if (apiDic.Count > 0)
                //{
                //    foreach (var item in apiDic.Keys.Order())
                //    {
                //        Console.WriteLine($"注册API:{item}");
                //    }
                //}

                await LoggerManager.GetSqlLogger().BatchProcess();
            })
            .ContinueWith(t =>
            {
                if (t.IsFaulted)
                {
                    Log.Error(t.Exception, "异步任务失败");
                }
            });

            LoadDbApi();

            builder.Use(async (ctx, next) =>
            {
                if (ctx.Request.Method == "OPTIONS")
                {
                    ctx.Response.StatusCode = 405;
                    return;
                }

                await next();

                if (ctx.Response.HasStarted) return;
                ctx.Response.StatusCode = 200;
                try
                {
                    var req = ctx.Request;
                    var url = req.Path.Value.ToLower().Trim();
                    var method = req.Method;
                    ctx.Request.Headers.TryGetValue("cid", out var clientId);
                    var cancellationToken = ctx.RequestAborted;

                    if (method == "POST" && url == "/gcp/function/run")
                    {
                        ctx.Response.ContentType = "application/json; charset=utf-8";

                        var invokeArgs = await JsonHelper.DeserializeAsync<FunctionInvokeDTO>(req.Body);
                        var result = await FunctionHelper.Runner.Execute(invokeArgs, (funCtx) =>
                        {
                            funCtx.SetHttpRequest(ctx.Request);
                            funCtx.clientID = clientId;
                            funCtx.CancellationToken = cancellationToken;
                            return Task.CompletedTask;
                        }, BaseResponseMiddleware.Handler, AutoLoadMiddleware.Handler).ConfigureAwait(false);

                        await JsonHelper.SerializeAsync(ctx.Response.Body, result);
                    }
                    else if (method == "GET" && url == "/gcp/log")
                    {
                        try
                        {
                            var log = LoggerManager.GetLogger(clientId);
                            await foreach (var msg in log.ReadAllAsync(cancellationToken).ConfigureAwait(false))
                            {
                                await ctx.Response.WriteAsync(msg, cancellationToken: cancellationToken);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            //Console.WriteLine($"client: {clientId} 操作已取消");
                            ctx.Abort();
                        }
                    }
                    else
                    {
                        if (LicenseManager.IsActivated == null || !LicenseManager.IsActivated.Value)
                        {
                            throw new CustomException("请联系管理员激活后使用，注册码：" + LicenseManager.GenerateRegistrationCode(), 401);
                        }

                        await RunSystemApiAsync(ctx, req, url, method, cancellationToken).ConfigureAwait(false);
                    }
                }
                catch (CustomException ex)
                {
                    if (ctx.Response.HasStarted) return;
                    ctx.Response.StatusCode = ex.Code ?? 500;
                    await ctx.Response.WriteAsync(ex.Message);
                }
                catch (Exception ex)
                {
                    if (!ctx.Response.HasStarted)
                        ctx.Response.StatusCode = 500;
                    throw ex.InnerException ?? ex;
                }
            });

            return builder;
        }

        internal static async Task<object> RunApiAsync(string funcId, Dictionary<string, object> args, ApiInfoDTO apiInfo, CancellationToken cancellationToken = default, HttpContext httpContext = null)
        {
            var funcInfo = FunctionRunner.GetFunctionInfo(funcId);
            if (funcInfo?.FunctionProxy == null)
            {
                throw new CustomException("未找到对应函数：" + funcId);
            }

            var hasHttpContext = httpContext != null;
            var originalBody = hasHttpContext ? httpContext.Response.Body : null;

            // 准备全局数据
            using var tempContext = new FunctionContext();
            tempContext.LocalDbContext.Value = tempContext.scope.ServiceProvider.GetRequiredService<IDbContext>();
            var dicService = new DataDictionaryService(tempContext, funcInfo.SolutionId, funcInfo.ProjectId);
            dicService.Cache = _cache;
            var sysParamDict = await dicService.GetSysParamDict();

            var globalData = new Dictionary<string, object>
            {
                ["$global"] = sysParamDict
            };

            // 创建执行配置
            var config = FunctionExecutionConfigBuilder.ForApi(
                funcInfo.SolutionId,
                funcInfo.ProjectId,
                funcId,
                apiInfo.Basice.ApiName,
                args,
                globalData,
                (decimal?)(httpContext?.Request.ContentLength / 1024.0));

            // 设置最终流量计算器和清理操作
            long responseLength = 0;
            config.FinalTrafficCalculator = () => (decimal)(responseLength / 1024.0);
            config.CleanupAction = (context, result, procLog) =>
            {
                context.Dispose();
            };

            // 执行函数
            var result = await FunctionExecutionHelper.ExecuteWithLogging(config, async (context) =>
            {
                if (hasHttpContext) context.SetHttpRequest(httpContext.Request);

                var engine = FlowUtils.GetEngine();
                var isSuccess = false;
                Dictionary<string, object> localResponseDic = new()
                {
                    ["success"] = isSuccess,
                    ["errorMessage"] = null,
                    ["result"] = null
                };

                object functionResult = null;
                try
                {
                    functionResult = await Runner.Execute(funcInfo, context).ConfigureAwait(false);

                    isSuccess = true;
                    localResponseDic["success"] = isSuccess;
                    localResponseDic["result"] = functionResult;
                }
                catch (Exception ex)
                {
                    localResponseDic["errorMessage"] = ex.Message;
                    localResponseDic["result"] = null;
                    functionResult = null;
                    Log.Error("{url} {name} 执行失败, {errorMessage}", apiInfo.Basice.HttpUrl, apiInfo.Basice.ApiName, ex.Message);
                    throw;
                }
                finally
                {
                    if (apiInfo.HasResponse)
                    {
                        var responseFlowData = apiInfo.ResponseData;
                        if (responseFlowData != null && responseFlowData.Count > 0)
                        {
                            foreach (var item in localResponseDic)
                            {
                                engine.SetValue(item.Key, item.Value);
                            }

                            var responseDic = new Dictionary<string, object>();
                            functionResult = FlowUtils.BindResult(responseFlowData, responseDic, engine, context, functionResult, localResponseDic);
                        }
                    }
                }

                return functionResult;
            }, cancellationToken);

            // 处理HTTP响应
            if (httpContext is { Response.Body: MemoryStream ms })
            {
                if (result is not string)
                {
                    ms.SetLength(0);
                    await JsonHelper.SerializeAsync(ms, result).ConfigureAwait(false);
                }

                ms.Position = 0;
                httpContext.Response.Headers.ContentLength = ms.Length;
                await ms.CopyToAsync(originalBody, cancellationToken).ConfigureAwait(false);
                responseLength = ms.Length;
            }
            else if (hasHttpContext)
            {
                using var responseBody = new MemoryStream();
                await JsonHelper.SerializeAsync(responseBody, result).ConfigureAwait(false);
                responseBody.Position = 0;
                await responseBody.CopyToAsync(originalBody, cancellationToken).ConfigureAwait(false);
                responseLength = responseBody.Length;
            }

            return result;
        }

        static readonly FunctionRunner Runner = new FunctionRunner();
        internal static async Task RunSystemApiAsync(HttpContext httpContext, HttpRequest req, string url, string method, CancellationToken cancellationToken)
        {
            httpContext.Response.ContentType = "application/json; charset=utf-8";

            if (!ApiDic.TryGetValue($"{method} {url}", out var apiInfo))
            {
                if (httpContext.Response.HasStarted) return;
                httpContext.Response.StatusCode = 404;
                await JsonHelper.SerializeAsync(httpContext.Response.Body, new ResponseBase
                {
                    Code = 500,
                    Message = "未找到匹配服务, 请确认是否配置"
                });
                return;
            }

            var args = new Dictionary<string, object>();
            try
            {
                await SetApiArgs(req, apiInfo, method, args).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                if (httpContext.Response.HasStarted) return;
                httpContext.Response.StatusCode = 400;
                await JsonHelper.SerializeAsync(httpContext.Response.Body, new ResponseBase
                {
                    Code = 500,
                    Message = "请求参数解析失败：" + ex.Message
                });
                return;
            }

            await RunApiAsync(apiInfo.Basice.FunctionId, args, apiInfo, cancellationToken, httpContext).ConfigureAwait(false);
        }

        private static async Task SetApiArgs(HttpRequest req, ApiInfoDTO apiInfo, string method, Dictionary<string, object> args)
        {
            if (apiInfo.HasHeader)
            {
                var headerFlowData = apiInfo.HeaderData;
                if (headerFlowData is { Count: > 0 })
                {
                    foreach (var item in headerFlowData)
                    {
                        var value = req.Headers[item.Key];
                        args[item.Key] = item.ParseDefaultValue(value.ToString());
                    }
                }
            }

            if (apiInfo.HasParams)
            {
                var queryFlowData = apiInfo.ParamsData;
                if (queryFlowData is { Count: > 0 })
                {
                    // 使用通用的ROOT节点处理方法
                    var (processedData, rootNode) = RootNodeHelper.ProcessSystemApiQueryParams(req.Query, queryFlowData);

                    if (rootNode != null)
                    {
                        // 有ROOT节点，使用处理后的数据
                        args[rootNode.Key] = rootNode.ParseDefaultValue(processedData);
                    }
                    else
                    {
                        // 没有ROOT节点，使用原有逻辑
                        foreach (var item in queryFlowData)
                        {
                            args[item.Key] = item.ParseDefaultValue(req.Query[item.Key].ToString());
                        }
                    }
                }
            }

            if (apiInfo.HasBody)
            {
                if (method == "GET")
                {
                    return;
                }

                var bodyFlowData = apiInfo.BodyData;
                if (bodyFlowData is { Count: > 0 })
                {
                    // 使用通用的ROOT节点处理方法
                    await req.ReadBodyAsync(async (ms) =>
                    {
                        var (processedData, rootNode) = await RootNodeHelper.ProcessSystemApiRequestBody(ms, bodyFlowData);

                        if (rootNode != null)
                        {
                            // 有ROOT节点，使用处理后的数据
                            args[rootNode.Key] = rootNode.ParseDefaultValue(processedData);
                        }
                        else
                        {
                            // 没有ROOT节点，使用原有逻辑
                            var body = (Dictionary<string, object>)processedData;
                            foreach (var item in bodyFlowData)
                            {
                                if (body.TryGetValue(item.Key, out object value))
                                {
                                    args[item.Key] = item.ParseDefaultValue(value);
                                }
                                else
                                {
                                    args[item.Key] = item.ParseDefaultValue(null);
                                }
                            }
                        }
                    });
                }
            }
        }
    }
}
