import{d,h as a,ab as L,ac as O,ad as y}from"./index-CmSBegZC.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 25 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.2256 -0.00341797H13.2295V2.00049H11.2256V-0.00341797ZM4.4494 2.80335L5.86631 4.22039L4.4494 5.6373L3.03236 4.22039L4.4494 2.80335ZM20.006 2.80357L21.4226 4.22033L20.006 5.63721L18.5883 4.22033L20.006 2.80357ZM4.22656 12.0005C4.22656 7.58221 7.80828 4.00049 12.2266 4.00049C16.6448 4.00049 20.2266 7.58221 20.2266 12.0005C20.2266 16.4188 16.6448 20.0005 12.2266 20.0005C7.80828 20.0005 4.22656 16.4188 4.22656 12.0005ZM0.225586 10.9966H2.22949V13.0005H0.225586V10.9966ZM22.2256 10.9966H24.2295V13.0005H22.2256V10.9966ZM4.44936 18.3598L5.86616 19.7765L4.44936 21.1941L3.03257 19.7765L4.44936 18.3598ZM20.006 18.36L21.4224 19.7765L20.006 21.1939L18.5885 19.7765L20.006 18.36ZM11.2256 21.9966H13.2295V24.0005H11.2256V21.9966Z"}}]},h=d({name:"ModeLightFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=L(r),p=a(()=>["t-icon","t-icon-mode-light-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(g,f.value)}});export{h as default};
