import{d as L,h as a,ab as O,ac as y,ad as d}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16.362 2.59529L14.6907 5.64567C13.6459 7.5525 11.9462 9.01665 9.90565 9.76748L6.07717 11.1762L5.33977 17.2428L11.0011 11.5815L12.4153 12.9957L6.75419 18.6568L12.8183 17.9194L14.2359 14.0354C14.9703 12.0233 16.3982 10.3391 18.263 9.28548L21.315 7.56102L22.2989 9.30229L19.2469 11.0267C17.7965 11.8463 16.6859 13.1562 16.1147 14.7211L14.2767 19.7567L2.85059 21.1463L4.23928 9.72135L9.21501 7.89051C10.8021 7.30653 12.1241 6.16775 12.9367 4.68466L14.608 1.63428L16.362 2.59529Z"}}]},g=L({name:"PenIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-pen",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
