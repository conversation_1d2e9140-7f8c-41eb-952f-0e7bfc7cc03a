import{d as z,r as E,f as r,b as c,o as i,n as a,a as p,g as e,t as m,c as D,F as w,s as T,u as b,p as u,M as _,_ as M}from"./index-CmSBegZC.js";const L={name:[{required:!0,message:"请输入合同名称",type:"error"}],type:[{required:!0,message:"请选择合同类型",type:"error"}],payment:[{required:!0,message:"请选择合同收付类型",type:"error"}],amount:[{required:!0,message:"请输入合同金额",type:"error"}],partyA:[{required:!0,message:"请选择甲方",type:"error"}],partyB:[{required:!0,message:"请选择乙方",type:"error"}],signDate:[{required:!0,message:"请选择日期",type:"error"}],startDate:[{required:!0,message:"请选择日期",type:"error"}],endDate:[{required:!0,message:"请选择日期",type:"error"}]},Y={name:"",type:"",partyA:"",partyB:"",signDate:"",startDate:"",endDate:"",payment:"1",amount:0,comment:"",files:[]},$=[{label:"Type A",value:"1"},{label:"Type B",value:"2"},{label:"Type C",value:"3"}],j=[{label:"Company A",value:"1"},{label:"Company B",value:"2"},{label:"Company C",value:"3"}],G=[{label:"Company A",value:"1"},{label:"Company B",value:"2"},{label:"Company C",value:"3"}],H={class:"form-basic-container"},J={class:"form-basic-item"},K={class:"form-basic-container-title"},Q={class:"form-basic-container-title form-title-gap"},W={class:"form-submit-container"},X={class:"form-submit-sub"},Z={class:"form-submit-left"},x={name:"FormBase"},ee=z({...x,setup(ae){const s=E({...Y}),P=()=>{_.warning("取消新建")},C=l=>{l.validateResult===!0&&_.success("新建成功")},U=l=>/\.(pdf)$/.test(l.name)?l.size>60*1024*1024?(_.warning("上传文件不能大于60M"),!1):!0:(_.warning("请上传pdf文件"),!1),k=l=>{_.error(`文件 ${l.file.name} 上传失败`)},q=l=>({...l,error:"上传失败，请重试",url:l.url});return(l,o)=>{const A=r("t-input"),n=r("t-form-item"),d=r("t-col"),y=r("t-option"),v=r("t-select"),h=r("t-radio"),I=r("t-radio-group"),g=r("t-date-picker"),B=r("t-button"),N=r("t-upload"),S=r("t-row"),F=r("t-textarea"),V=r("t-avatar"),O=r("t-avatar-group"),R=r("t-form");return i(),c(R,{ref:"form",class:"base-form",data:s.value,rules:b(L),"label-align":"top","label-width":100,onReset:P,onSubmit:C},{default:a(()=>[p("div",H,[p("div",J,[p("div",K,m(l.t("pages.formBase.title")),1),e(S,{class:"row-gap",gutter:[32,24]},{default:a(()=>[e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.contractName"),name:"name"},{default:a(()=>[e(A,{modelValue:s.value.name,"onUpdate:modelValue":o[0]||(o[0]=t=>s.value.name=t),style:{width:"322px"},placeholder:"请输入内容"},null,8,["modelValue"])]),_:1},8,["label"])]),_:1}),e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.contractType"),name:"type"},{default:a(()=>[e(v,{modelValue:s.value.type,"onUpdate:modelValue":o[1]||(o[1]=t=>s.value.type=t),style:{width:"322px"},class:"demo-select-base",clearable:""},{default:a(()=>[(i(!0),D(w,null,T(b($),(t,f)=>(i(),c(y,{key:f,value:t.value,label:t.label},{default:a(()=>[u(m(t.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1}),e(d,{span:8},{default:a(()=>[e(n,{label:l.t("pages.formBase.contractPayType"),name:"payment"},{default:a(()=>[e(I,{modelValue:s.value.payment,"onUpdate:modelValue":o[2]||(o[2]=t=>s.value.payment=t)},{default:a(()=>[e(h,{value:"1"},{default:a(()=>[u(m(l.t("pages.formBase.receive")),1)]),_:1}),e(h,{value:"2"},{default:a(()=>[u(m(l.t("pages.formBase.pay")),1)]),_:1})]),_:1},8,["modelValue"]),o[10]||(o[10]=p("span",{class:"space-item"},null,-1)),p("div",null,[e(A,{placeholder:l.t("pages.formBase.contractAmountPlaceholder"),style:{width:"160px"}},null,8,["placeholder"])])]),_:1,__:[10]},8,["label"])]),_:1}),e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.company"),name:"partyA"},{default:a(()=>[e(v,{modelValue:s.value.partyA,"onUpdate:modelValue":o[3]||(o[3]=t=>s.value.partyA=t),style:{width:"322px"},class:"demo-select-base",placeholder:l.t("pages.formBase.contractTypePlaceholder"),clearable:""},{default:a(()=>[(i(!0),D(w,null,T(b(j),(t,f)=>(i(),c(y,{key:f,value:t.value,label:t.label},{default:a(()=>[u(m(t.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.employee"),name:"partyB"},{default:a(()=>[e(v,{modelValue:s.value.partyB,"onUpdate:modelValue":o[4]||(o[4]=t=>s.value.partyB=t),style:{width:"322px"},placeholder:l.t("pages.formBase.contractTypePlaceholder"),class:"demo-select-base",clearable:""},{default:a(()=>[(i(!0),D(w,null,T(b(G),(t,f)=>(i(),c(y,{key:f,value:t.value,label:t.label},{default:a(()=>[u(m(t.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.contractSignDate"),name:"signDate"},{default:a(()=>[e(g,{modelValue:s.value.signDate,"onUpdate:modelValue":o[5]||(o[5]=t=>s.value.signDate=t),style:{width:"322px"},theme:"primary",mode:"date",separator:"/"},null,8,["modelValue"])]),_:1},8,["label"])]),_:1}),e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.contractEffectiveDate"),name:"startDate"},{default:a(()=>[e(g,{modelValue:s.value.startDate,"onUpdate:modelValue":o[6]||(o[6]=t=>s.value.startDate=t),style:{width:"322px"},theme:"primary",mode:"date",separator:"/"},null,8,["modelValue"])]),_:1},8,["label"])]),_:1}),e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.contractEndDate"),name:"endDate"},{default:a(()=>[e(g,{modelValue:s.value.endDate,"onUpdate:modelValue":o[7]||(o[7]=t=>s.value.endDate=t),style:{width:"322px"},theme:"primary",mode:"date",separator:"/"},null,8,["modelValue"])]),_:1},8,["label"])]),_:1}),e(d,{span:6},{default:a(()=>[e(n,{label:l.t("pages.formBase.upload"),name:"files"},{default:a(()=>[e(N,{modelValue:s.value.files,"onUpdate:modelValue":o[8]||(o[8]=t=>s.value.files=t),action:"https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo",tips:l.t("pages.formBase.uploadTips"),"size-limit":{size:60,unit:"MB"},"format-response":q,"before-upload":U,onFail:k},{default:a(()=>[e(B,{class:"form-submit-upload-btn",variant:"outline"},{default:a(()=>[u(m(l.t("pages.formBase.uploadFile")),1)]),_:1})]),_:1},8,["modelValue","tips"])]),_:1},8,["label"])]),_:1})]),_:1}),p("div",Q,m(l.t("pages.formBase.otherInfo")),1),e(n,{label:l.t("pages.formBase.remark"),name:"comment"},{default:a(()=>[e(F,{modelValue:s.value.comment,"onUpdate:modelValue":o[9]||(o[9]=t=>s.value.comment=t),height:124,placeholder:l.t("pages.formBase.remarkPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(n,{label:l.t("pages.formBase.notaryPublic")},{default:a(()=>[e(O,null,{default:a(()=>[e(V,null,{default:a(()=>o[11]||(o[11]=[u("D",-1)])),_:1,__:[11]}),e(V,null,{default:a(()=>o[12]||(o[12]=[u("S",-1)])),_:1,__:[12]}),e(V,null,{default:a(()=>o[13]||(o[13]=[u("+",-1)])),_:1,__:[13]})]),_:1})]),_:1},8,["label"])])]),p("div",W,[p("div",X,[p("div",Z,[e(B,{theme:"primary",class:"form-submit-confirm",type:"submit"},{default:a(()=>[u(m(l.t("pages.formBase.confirm")),1)]),_:1}),e(B,{type:"reset",class:"form-submit-cancel",theme:"default",variant:"base"},{default:a(()=>[u(m(l.t("pages.formBase.cancel")),1)]),_:1})])])])]),_:1},8,["data","rules"])}}}),te=M(ee,[["__scopeId","data-v-2d219962"]]);export{te as default};
