import{d as f,h as a,ab as O,ac as y,ad as b}from"./index-CmSBegZC.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){b(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M20 23H4V1H20V6H18V3H6V21H18V18H20V23ZM13.0039 19.0039H11V17H13.0039V19.0039ZM21.8865 16.2869L23.4553 15.506L21.8865 14.7251L21.1056 13.1563L20.3247 14.7251L18.7559 15.506L20.3247 16.2869L21.1056 17.8557L21.8865 16.2869ZM16.0572 15.3007L14.9601 13.0967L12.7561 11.9996L14.9601 10.9025L16.0572 8.69844L17.1543 10.9025L19.3583 11.9996L17.1543 13.0967L16.0572 15.3007ZM21.8865 9.27714L23.4553 8.49625L21.8865 7.71534L21.1056 6.14653L20.3247 7.71534L18.7559 8.49625L20.3247 9.27714L21.1056 10.8459L21.8865 9.27714Z"}}]},h=f({name:"MobileShortcutIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:s}=O(r),p=a(()=>["t-icon","t-icon-mobile-shortcut",o.value]),u=a(()=>c(c({},s.value),t.style)),L=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(d,L.value)}});export{h as default};
