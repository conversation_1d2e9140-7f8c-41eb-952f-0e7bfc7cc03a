import{d as O,h as a,ab as y,ac as d,ad as m}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17.85 1H6.15V4.59026L4 9.49026V23H20V9.49026L17.85 4.59026V1ZM16.9322 7.47622L18 9.90974V21H16.05V9.87786L16.9322 7.47622ZM14.05 10.7V21H6V10.7H14.05ZM6.5308 8.7L7.80325 5.8H15.4173L14.352 8.7H6.5308ZM8.15 3.8V3H15.85V3.8H8.15ZM13 13H11.5743L9.9984 14.5838L8.41536 13H7V19H9V16.4141L10.0016 17.4162L11 16.4127V19H13V13Z"}}]},b=O({name:"MilkIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=y(t),p=a(()=>["t-icon","t-icon-milk",l.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>d(V,v.value)}});export{b as default};
