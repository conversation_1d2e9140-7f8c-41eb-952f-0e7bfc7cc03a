import{_ as d,f as n,c as i,o as p,g as c,a as o,t,n as r,p as u}from"./index-CmSBegZC.js";const _={name:"ResultSuccess"},f={class:"result-success"},m={class:"result-success-title"},b={class:"result-success-describe"};function g(s,e,v,h,k,$){const l=n("t-icon"),a=n("t-button");return p(),i("div",f,[c(l,{class:"result-success-icon",name:"check-circle"}),o("div",m,t(s.t("pages.result.success.title")),1),o("div",b,t(s.t("pages.result.success.subtitle")),1),o("div",null,[c(a,{theme:"default",onClick:e[0]||(e[0]=()=>s.$router.push("/detail/advanced"))},{default:r(()=>[u(t(s.t("pages.result.success.progress")),1)]),_:1}),c(a,{onClick:e[1]||(e[1]=()=>s.$router.push("/dashboard/base"))},{default:r(()=>[u(t(s.t("pages.result.success.back")),1)]),_:1})])])}const B=d(_,[["render",g],["__scopeId","data-v-04161c74"]]);export{B as default};
