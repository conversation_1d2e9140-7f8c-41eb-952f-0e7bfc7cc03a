import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7 5C5.34315 5 4 6.34315 4 8C4 9.65685 5.34315 11 7 11V13C4.23858 13 2 15.2386 2 18V22H0V18C0 15.3075 1.52021 12.97 3.74913 11.7991C2.67847 10.882 2 9.52024 2 8C2 5.23858 4.23858 3 7 3H8V4.99951C8.91223 3.78534 10.3644 3 12 3C13.6356 3 15.0878 3.78534 16 4.99951V3H17C19.7614 3 22 5.23858 22 8C22 9.52024 21.3215 10.882 20.2509 11.7991C22.4798 12.97 24 15.3075 24 18V22H22V18C22 15.2386 19.7614 13 17 13V11C18.6569 11 20 9.65685 20 8C20 6.34315 18.6569 5 17 5L16 4.99951C16.6277 5.83517 17 6.87439 17 8C17 10.7614 14.7614 13 12 13C9.23858 13 7 10.7614 7 8C7 6.87439 7.37231 5.83517 8 4.99951L7 5ZM12 5C10.3431 5 9 6.34315 9 8C9 9.65685 10.3431 11 12 11C13.6569 11 15 9.65685 15 8C15 6.34315 13.6569 5 12 5ZM4 19C4 16.2386 6.23858 14 9 14H15C17.7614 14 20 16.2386 20 19V22H4V19ZM9 16C7.34315 16 6 17.3431 6 19V20H18V19C18 17.3431 16.6569 16 15 16H9Z"}}]},b=f({name:"UsergroupIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:C}=O(t),c=a(()=>["t-icon","t-icon-usergroup",o.value]),p=a(()=>s(s({},C.value),r.style)),u=a(()=>({class:c.value,style:p.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(g,u.value)}});export{b as default};
