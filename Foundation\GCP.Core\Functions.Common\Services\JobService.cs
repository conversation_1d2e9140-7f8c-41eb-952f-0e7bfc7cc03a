using EasyCaching.Core;
using GCP.Common;
using GCP.Common.Job;
using GCP.DataAccess;
using GCP.FunctionPool;
using GCP.Functions.Common.Models;
using LinqToDB;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System.Diagnostics;
using System.Text;
using System.Text.Json;

namespace GCP.Functions.Common.Services
{
    [Function("job", "任务管理器JOB服务")]
    class JobService : BaseService
    {
        public IJob Job { get; set; }

        private static string JobKeyPrefix => "BU:";

        private static string JobPipelineKeyPrefix => "$Job_";

        public JobService() : base()
        {
            Job = ServiceLocator.Current.GetService(typeof(IJob)) as IJob;
            FunctionHelper.TriggerJob = TriggerByFunctionId;
        }

        [Function("refresh", "刷新所有JOB配置")]
        [Job(JobId = "RefreshJobs", JobCron = "0 0/5 * * * ?", JobName = "刷新所有JOB配置")]
        public void Refresh()
        {
            using var db = this.GetDb();
            var jobs = db.LcJobs.Where(t => t.State == 1 && t.Status == 1).ToList();

            var existedJobIds = new List<string>();
            foreach (var job in jobs)
            {
                var id = job.Id;
                var cron = job.CronExpression;

                try
                {
                    _ = CronHelper.GetCornNextOccurrence(cron).FirstOrDefault();
                    var jobFunc = GetRunJobFunc(job);
                    Job.AddOrUpdate(JobKeyPrefix + id, jobFunc, cron, job.JobName, job.Description);
                }
                catch (Exception ex)
                {
                    Log.Error($"请检查JOB【{job.JobName}】CRON表达式是否配置错误！" + ex.Message);
                }

                existedJobIds.Add(JobKeyPrefix + id);
            }

            foreach (var id in Job.GetAllJobIds())
            {
                if (id.StartsWith(JobKeyPrefix) && !existedJobIds.Contains(id))
                {
                    Job.RemoveIfExists(id);
                }
            }
        }

        [Function("trigger", "执行一次JOB")]
        public async Task Trigger(string[] ids, Dictionary<string, object> args = null)
        {
            if (ids == null || ids.Length == 0)
            {
                throw new CustomException("错误的JOB ID");
            }
            var jobIds = Job.GetAllJobIds();

            await using var db = this.GetDb();
            var jobs = db.LcJobs.Where(t => ids.Contains(t.Id)).ToList();

            await TriggerByJobs(jobIds, jobs, args).ConfigureAwait(false);

            //var jobIds = Job.GetAllJobIds();
            //foreach (var id in ids)
            //{
            //    if (jobIds.Contains(id))
            //    {
            //        Job.Trigger(JOB_PREFIX + id);
            //    }
            //    else
            //    {

            //    }
            //}
        }

        internal async Task TriggerByFunctionId(string funcId, Dictionary<string, object> args)
        {
            var jobIds = Job.GetAllJobIds();

            await using var db = this.GetDb();
            var jobs = db.LcJobs.Where(t => t.FunctionId == funcId).ToList();
            if (jobs.Count == 0)
            {
                throw new CustomException("未找到JOB");
            }
            else if (jobs.Count > 1)
            {
                throw new CustomException("JOB 对应函数不唯一");
            }

            await TriggerByJobs(jobIds, jobs, args).ConfigureAwait(false);
        }

        internal async Task TriggerByJobs(List<string> jobIds, List<LcJob> jobs, Dictionary<string, object> args = null)
        {
            foreach (var job in jobs)
            {
                //if (jobIds.Contains(JOB_PREFIX + job.Id) && (args == null || args.Count == 0))
                //{
                //    Job.Trigger(JOB_PREFIX + job.Id);
                //    continue;
                //}

                try
                {
                    // 统一使用jobFunc方式执行，避免双重触发
                    var jobFunc = GetRunJobFunc(job, args);
                    await jobFunc().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "触发Job {jobId} {jobName} 失败", job.Id, job.JobName);
                }
            }
        }

        [Function("getById", "获取JOB详情")]
        public LcJob GetById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcJobs.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                throw new CustomException("未找到JOB");
            }
            return data;
        }

        [Function("getAll", "获取JOB清单")]
        public PagingData<JobInfoVO> GetAll(string keyword = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcJobs
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(keyword) || (a.JobName.Contains(keyword) || a.JobGroup.Contains(keyword) || a.Description.Contains(keyword)))
                        orderby new { a.JobGroup, a.JobName }
                        select new JobInfoVO
                        {
                            Id = a.Id,
                            JobName = a.JobName,
                            JobGroup = a.JobGroup,
                            Description = a.Description,
                            CronExpression = a.CronExpression,
                            AllowParallelization = a.AllowParallelization ?? 0,
                            LogRetentionDays = a.LogRetentionDays,
                            AllowRetry = a.AllowRetry ?? 0,
                            RetryCount = a.RetryCount,
                            RetryDelaysInSeconds = a.RetryDelaysInSeconds,
                            AllowErrorNotification = a.AllowErrorNotification ?? 0,
                            NotificationEmail = a.NotificationEmail,
                            Status = a.Status,
                            JobBeginTime = a.JobBeginTime,
                            JobEndTime = a.JobEndTime,
                            FunctionId = a.FunctionId,
                            ExceptionStop = a.ExceptionStop ?? 0,
                            NextBeginTime = a.NextBeginTime,
                            CronDescription = "",
                            NextTime = null
                        }).ToPagingData(pageIndex, pageSize);

            foreach (var job in data.List)
            {
                job.CronDescription = CronHelper.GetCronDescription(job.CronExpression);
                job.NextTime = CronHelper.GetCornNextOccurrence(job.CronExpression).FirstOrDefault();
            }
            return data;
        }

        [Function("getAllGroup", "获取JOB任务组")]
        public List<string> GetAllGroup()
        {
            using var db = this.GetDb();
            var data = (from a in db.LcJobs
                        where a.State == 1
                        select a.JobGroup).Distinct().ToList();
            return data;
        }

        [Function("add", "新增JOB")]
        public void AddJob(LcJob job)
        {
            job.SolutionId = this.SolutionId;
            job.ProjectId = this.ProjectId;
            job.Status ??= 0;

            if (string.IsNullOrWhiteSpace(job.CronExpression))
            {
                throw new CustomException("CRON表达式不能为空");
            }

            try
            {
                var nextTime = CronHelper.GetCornNextOccurrence(job.CronExpression).FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new CustomException("CRON表达式格式错误, 请检查！" + ex.Message);
            }

            using var db = this.GetDb();
            var existedJob = db.LcJobs
                .FirstOrDefault(t => t.SolutionId == job.SolutionId && t.ProjectId == job.ProjectId && t.JobName == job.JobName && t.JobGroup == job.JobGroup);

            db.BeginTransaction();
            if (existedJob != null)
            {
                if (existedJob.State == 1)
                {
                    throw new CustomException($"JOB {existedJob.JobName} 已存在");
                }
                else
                {
                    db.LcJobs.Delete(t => t.Id == existedJob.Id);
                }
                
            }

            job.FunctionId = AddFunction(job.JobName, job.Description, db);

            this.InsertData(job, db);
            db.CommitTransaction();

            if (job.Status == 1)
            {
                this.Refresh();
            }
        }

        private string AddFunction(string functionName, string description, GcpDb db = null)
        {

            LcFunction function = new()
            {
                SolutionId = this.SolutionId,
                ProjectId = this.ProjectId,
                FunctionName = functionName,
                FunctionType = "FLOW",
                Description = description,
                UseVersion = 1
            };
            return this.InsertData(function);
        }

        [Function("updateState", "更新JOB状态")]
        public bool UpdateState(string[] ids, short status)
        {
            using var db = this.GetDb();
            var data = db.LcJobs
                .Where(t => ids.Contains(t.Id))
                .Set(t => t.Status, status);
            var rowsAffected = this.UpdateData(data);
            var result = rowsAffected > 0;
            if (result)
            {
                this.Refresh();
            }
            return result;
        }

        [Function("updateFunction", "更新JOB状态")]
        public bool UpdateFunction(string id, string functionId)
        {
            using var db = this.GetDb();
            var data = db.LcJobs
                .Where(t => id == t.Id)
                .Set(t => t.FunctionId, functionId);
            var rowsAffected = this.UpdateData(data);
            var result = rowsAffected > 0;
            return result;
        }

        [Function("update", "更新JOB")]
        public bool Update(LcJob job)
        {
            using var db = this.GetDb();
            var data = db.LcJobs.FirstOrDefault(t => t.Id == job.Id);
            if (data == null)
            {
                return false;
            }
            data.JobName = job.JobName;
            data.JobGroup = job.JobGroup;
            data.Description = job.Description;
            data.CronExpression = job.CronExpression;
            data.AllowParallelization = job.AllowParallelization;
            data.LogRetentionDays = job.LogRetentionDays;
            data.AllowRetry = job.AllowRetry;
            data.RetryCount = job.RetryCount;
            data.RetryDelaysInSeconds = job.RetryDelaysInSeconds;
            data.AllowErrorNotification = job.AllowErrorNotification;
            data.NotificationEmail = job.NotificationEmail;
            data.ExceptionStop = job.ExceptionStop;
            data.TimeoutInSeconds = job.TimeoutInSeconds;

            db.BeginTransaction();
            if (string.IsNullOrEmpty(data.FunctionId))
            {
                data.FunctionId = AddFunction(job.JobName, job.Description, db);
            }
            else if (data.JobName != job.JobName || data.Description != job.Description)
            {
                var updateFunction = db.LcFunctions
                    .Where(t => t.Id == data.FunctionId)
                    .Set(t => t.FunctionName, job.JobName)
                    .Set(t => t.Description, job.Description);

                this.UpdateData(updateFunction);
            }

            this.UpdateData(data, db);
            db.CommitTransaction();

            ResiliencePipelineManager.TryRemove(JobPipelineKeyPrefix + job.Id);
            return true;
        }

        [Function("delete", "删除JOB")]
        public bool Delete(string id)
        {
            Job.RemoveIfExists(JobKeyPrefix + id);

            using var db = this.GetDb();
            var data = db.LcJobs.FirstOrDefault(t => t.Id == id);
            if (data == null)
            {
                return false;
            }
            data.State = 0;
            this.UpdateData(data, db);

            ResiliencePipelineManager.TryRemove(JobPipelineKeyPrefix + id);

            if (!string.IsNullOrEmpty(data.FunctionId))
            {
                this.UpdateData(db.LcFunctions.Where(t => t.Id == data.FunctionId).Set(t => t.State, (short)0));
            }
            return true;
        }

        [Function("logs", "获取JOB日志")]
        public PagingData<LcJobLog> Logs(string id, short? status = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcJobLogs
                        where a.Id == id &&
                        (status == null || a.Status == status)
                        orderby a.JobBeginTime descending
                        select a).ToPagingData(pageIndex, pageSize);
            return data;
        }

        [Function("cleanupLogs", "清理JOB日志")]
        //[Job(JobId = "CleanupJobLogs", JobCron = "0 0 2 1/1 * ?", JobName = "清理JOB日志")]
        public void CleanupJobLogs()
        {
            using var db = this.GetDb();
            var cleanupJobList = db.LcJobs.Where(t => t.State == 1 && t.JobBeginTime != null && t.LogRetentionDays != null && t.LogRetentionDays > 0).ToList();

            var jobIds = new List<string>();
            var now = db.GetDbTime();
            foreach (var job in cleanupJobList)
            {
                if (job.LogRetentionDays == null) continue;
                var logRetentionDays = job.LogRetentionDays.Value;
                var logTime = now.AddDays(-logRetentionDays);
                if (job.JobBeginTime != null && job.JobBeginTime.Value < logTime)
                {
                    jobIds.Add(job.Id);
                }
            }

            if (jobIds.Count > 0)
            {
                db.LcJobLogs.Delete(t => jobIds.Contains(t.JobId));
            }
        }

        [Function("cronInfo", "获取Cron表达式相关信息")]
        public object CronInfo(string cron)
        {
            if (string.IsNullOrEmpty(cron))
            {
                throw new CustomException("CRON表达式不能为空");
            }

            //cron = FormatCronExpression(cron);
            var logRetentionDaysInfo = new DataDictionaryService(this).GetByCode("SYS_PARAM", "FlowLogRetentionDays");
            int logRetentionDays = logRetentionDaysInfo == null ? 7 : logRetentionDaysInfo.DictValue.Parse(7);

            return new
            {
                CronDescription = CronHelper.GetCronDescription(cron),
                NextTimes = CronHelper.GetCornNextOccurrence(cron, 5),
                LogRetentionDays = logRetentionDays,
            };
        }

        private Func<Task> GetRunJobFunc(LcJob job, Dictionary<string, object> args = null)
        {
            var pipelineKey = JobPipelineKeyPrefix + job.Id;
            ResiliencePipelineManager.TryAdd(pipelineKey, () =>
            {
                var handle = new ResilienceHandle<object>(pipelineKey);

                if (job.AllowParallelization != 1)
                {
                    handle.BulkheadMaxParallelization = 1;
                    handle.BulkheadMaxQueuingActions = 1;
                }

                if (job.AllowRetry == 1 && job.RetryCount > 0 && job.RetryDelaysInSeconds > 0)
                {
                    handle.RetryCount = job.RetryCount.Value;
                    handle.RetryDelaysInSeconds = job.RetryDelaysInSeconds.Value;
                }

                if (job.TimeoutInSeconds > 0)
                {
                    handle.Timeout = job.TimeoutInSeconds.Value;
                }

                return handle.Build((ex) =>
                {
                    Log.Error("{jobId} {jobName} 执行失败, {jobErrorMessage}", job.Id, job.JobName, ex.Message + ex.StackTrace);
                    if (job.AllowErrorNotification == 1)
                    {
                        //系统定时任务执行异常
                        //$"JOB {jobInfo.JOB_NAME} 执行异常, 请管理员排查具体原因！ {ex.Message} {ex.StackTrace}"
                    }
                    return Task.CompletedTask;
                });
            });

            args ??= [];

            return async () =>
            {
                var noContext = this.Context?.globalData == null;
                if (noContext)
                {
                    this.Init();
                }

                await using var db = this.GetDb();
                job = db.LcJobs.FirstOrDefault(t => t.Id == job.Id);
                if (job == null)
                {
                    Log.Error("{jobId} 不存在, 无法执行JOB", job.Id);
                    return;
                }

                if (job.State != 1)
                {
                    Log.Error("{jobId} {jobName} 已禁用, 无法执行JOB", job.Id, job.JobName);
                    return;
                }

                if (noContext)
                {
                    this.SetSolutionIdAndProjectId(job.SolutionId, job.ProjectId);
                }

                await ResiliencePipelineManager.ExecuteAsync(pipelineKey, async (token) =>
                {
                    await RunFlow(job, args, token).ConfigureAwait(false);
                }).ConfigureAwait(false);
            };
        }

        public async Task RunFlow(LcJob job, Dictionary<string, object> args, CancellationToken cancellationToken = default)
        {
            var logs = new StringBuilder();

            void AppendLog(string type, string msg)
            {
                logs.AppendLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss:fff")} {type} {msg}");
                if (type == "ERROR")
                    Log.Error(msg);
                else
                    Log.Debug(msg);
            }

            //var jobLog = new LcJobLog
            //{
            //    ProjectId = job.ProjectId,
            //    SolutionId = job.SolutionId,
            //    JobId = job.Id,
            //    JobName = job.JobName,
            //    JobGroup = job.JobGroup,
            //    JobBeginTime = DateTime.Now,
            //    Status = 1
            //};


            var functionId = job.FunctionId;
            if (string.IsNullOrEmpty(functionId))
            {
                return;
            }

            // 准备JOB参数
            args ??= [];
            var hasNextBeginTime = job.NextBeginTime.HasValue;
            var lastTime = job.NextBeginTime ?? job.JobBeginTime ?? DateTime.Now;

            if (args.TryGetValue("JOB_LAST_TIME", out var lastTimeObj))
            {
                if (lastTimeObj != null && DateTime.TryParse(lastTimeObj.ToString(), out var lastTimeArg))
                {
                    lastTime = lastTimeArg;
                }
            }

            if (args.TryGetValue("_JOB_LAST_TIME", out var lastTimeObj2))
            {
                switch (lastTimeObj2)
                {
                    case string lastTimeStr:
                        {
                            if (DateTime.TryParse(lastTimeStr, out var lastTimeArg2))
                            {
                                args["_JOB_LAST_TIME"] = lastTimeArg2;
                            }
                            break;
                        }
                    case JsonElement timeJson:
                        args["_JOB_LAST_TIME"] = JsonHelper.Deserialize<DateTime>(timeJson.GetRawText());
                        break;
                    default:
                        {
                            if (lastTimeObj2 is not DateTime)
                            {
                                throw new CustomException($"参数 _JOB_LAST_TIME 类型错误：{lastTimeObj2.GetType().Name}");
                            }
                            break;
                        }
                }
            }
            else if (hasNextBeginTime)
            {
                args["_JOB_LAST_TIME"] = lastTime;
            }
            else
            {
                // 需要临时创建context来获取字典服务
                using var tempContext = new FunctionContext();
                tempContext.LocalDbContext.Value = tempContext.scope.ServiceProvider.GetRequiredService<IDbContext>();
                var dicService = new DataDictionaryService(tempContext, job.SolutionId, job.ProjectId)
                {
                    Cache = this.Cache ?? ServiceLocator.Current.GetRequiredService<IEasyCachingProvider>()
                };

                var timeOffsetInfo = dicService.GetByCode("JOB_TASK", "TimeOffset");
                int timeOffset = timeOffsetInfo == null ? 0 : timeOffsetInfo.DictValue.Parse(0);
                var lastTimeOffset = lastTime.AddSeconds(-timeOffset);
                args["_JOB_LAST_TIME"] = lastTimeOffset;
            }
            args["JOB_LAST_TIME"] = args["_JOB_LAST_TIME"];

            // 创建执行配置
            var config = FunctionExecutionConfigBuilder.ForJob(
                job.SolutionId,
                job.ProjectId,
                job.FunctionId,
                job.JobName,
                this.UserName,
                args);

            // 设置清理操作
            config.CleanupAction = (context, result, procLog) =>
            {
                if (job.ExceptionStop != 1 || procLog.Status == 1)
                {
                    Task.Run(async () =>
                    {
                        await using var db = this.GetDb();
                        var updateJob = db.LcJobs.Where(t => t.Id == job.Id)
                            .Set(t => t.JobBeginTime, procLog.BeginTime)
                            .Set(t => t.JobEndTime, procLog.EndTime);

                        if (context.globalData.TryGetValue("_NEXT_BEGIN_TIME", out var value) && value is DateTime nextBeginTime)
                        {
                            updateJob = updateJob.Set(t => t.NextBeginTime, nextBeginTime);
                        }
                        else
                        {
                            updateJob = updateJob.Set(t => t.NextBeginTime, null as DateTime?);
                        }
                        this.UpdateData(updateJob);
                    });
                }
            };

            // 执行函数
            try
            {
                await FunctionExecutionHelper.ExecuteWithLogging(config, async (context) =>
                {
                    var dicService = new DataDictionaryService(context, job.SolutionId, job.ProjectId)
                    {
                        Cache = this.Cache ?? ServiceLocator.Current.GetRequiredService<IEasyCachingProvider>()
                    };

                    var sysParamDict = await dicService.GetSysParamDict();
                    context.globalData["$global"] = sysParamDict;

                    await FunctionHelper.Runner.Execute(new FunctionInvokeDTO
                    {
                        path = functionId,
                        args = args
                    }, (ctx) =>
                    {
                        ctx.Persistence = SqlLogSettings.JobStatus.Step;
                        ctx.trackId = context.trackId;
                        ctx.CancellationToken = cancellationToken;
                        if (SqlLogSettings.JobStatus.Step)
                            ctx.Middlewares.Add(TimeMiddleware.Handler);

                        ctx.globalData["$global"] = sysParamDict;
                        return Task.CompletedTask;
                    }).ConfigureAwait(false);

                    return null; // JOB通常不返回结果
                }, cancellationToken);
            }
            catch (CustomException ex) when (ex.Message.Contains("执行超时"))
            {
                AppendLog("ERROR", $"JOB执行超时：{ex.Message}");
                throw new CustomException($"JOB执行超时：任务在 {job.TimeoutInSeconds} 秒后被取消");
            }
            catch (Exception ex)
            {
                AppendLog("ERROR", $"未处理异常：{ex.Message}");
                throw;
            }
        }

        [Function("getFunctionNames", "获取JOB函数名称列表")]
        public List<OptionVO> GetFunctionNames()
        {
            using var db = this.GetDb();
            return (from a in db.LcJobs
                    where a.SolutionId == this.SolutionId
                    && a.ProjectId == this.ProjectId
                    && a.State == 1
                    && !string.IsNullOrEmpty(a.FunctionId)
                    select new OptionVO
                    {
                        Id = a.Id,
                        Value = a.FunctionId,
                        Label = a.JobName,
                        Description = a.Description
                    }).ToList();
        }
    }
}
