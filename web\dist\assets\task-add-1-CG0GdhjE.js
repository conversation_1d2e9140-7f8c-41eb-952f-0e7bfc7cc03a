import{d as f,h as a,ab as O,ac as y,ad as m}from"./index-CmSBegZC.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21H13V23H12C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C13.4978 1 14.9281 1.29991 16.2322 1.8439L17.1551 2.2289L16.3851 4.07474L15.4622 3.68974C14.3977 3.24571 13.2288 3 12 3ZM23.4142 4.5L12 15.9142L6.08579 10L7.5 8.58579L12 13.0858L22 3.08579L23.4142 4.5ZM20 15V18H23V20H20V23H18V20H15V18H18V15H20Z"}}]},g=f({name:"TaskAdd1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),p=a(()=>["t-icon","t-icon-task-add-1",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:d=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:d})}}));return()=>y(C,v.value)}});export{g as default};
