using GCP.DataAccess;
using GCP.Common;
using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.FunctionPool
{
    /// <summary>
    /// 函数执行助手，提供通用的函数执行日志记录和异常处理
    /// </summary>
    public static class FunctionExecutionHelper
    {
        /// <summary>
        /// 获取函数的当前使用版本
        /// </summary>
        /// <param name="functionId">函数ID</param>
        /// <param name="solutionId">解决方案ID</param>
        /// <param name="projectId">项目ID</param>
        /// <returns>当前使用版本</returns>
        private static long? GetFunctionVersion(string functionId, string solutionId, string projectId)
        {
            if (string.IsNullOrEmpty(functionId))
                return null;

            using var db = new GcpDb();

            var function = db.LcFunctions.FirstOrDefault(f => f.Id == functionId &&
                f.SolutionId == solutionId && f.ProjectId == projectId);
            return function?.UseVersion;
        }
        /// <summary>
        /// 执行函数的通用方法，包含完整的日志记录和异常处理
        /// </summary>
        /// <param name="config">函数执行配置</param>
        /// <param name="executeFunc">实际执行的函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<object> ExecuteWithLogging(
            FunctionExecutionConfig config,
            Func<FunctionContext, Task<object>> executeFunc,
            CancellationToken cancellationToken = default)
        {
            var procLog = new LcFruProc
            {
                Id = config.TrackId ?? TUID.NewTUID().ToString(),
                TimeCreate = DateTime.Now,
                Creator = config.Creator ?? "sys",
                SolutionId = config.SolutionId,
                ProjectId = config.ProjectId,
                FunctionId = config.FunctionId,
                FunctionName = config.FunctionName,
                Version = config.Version ?? GetFunctionVersion(config.FunctionId, config.SolutionId, config.ProjectId) ?? 1,
                TriggerType = config.TriggerType,
                Status = 0,
                BeginTime = DateTime.Now,
                TotalTraffic = config.InitialTraffic ?? 0,
            };

            var sw = new Stopwatch();
            var context = new FunctionContext();
            context.LocalDbContext.Value = context.scope.ServiceProvider.GetRequiredService<IDbContext>();
            context.trackId = procLog.Id;

            object result = null;

            try
            {
                // 设置上下文
                context.Args = config.Args ?? new Dictionary<string, object>();
                context.Persistence = config.LogSettings.Step;
                context.CancellationToken = cancellationToken;
                
                if (config.LogSettings.Step)
                    context.Middlewares.Add(TimeMiddleware.Handler);

                // 设置全局数据
                if (config.GlobalData != null)
                {
                    foreach (var item in config.GlobalData)
                    {
                        context.globalData[item.Key] = item.Value;
                    }
                }

                // 记录开始日志
                if (config.LogSettings.Flow)
                    await context.SqlLog.Write(procLog);

                sw.Start();

                // 执行实际函数
                result = await executeFunc(context);

                procLog.Status = 1;
            }
            catch (CustomSkipException)
            {
                sw.Stop();
                procLog.Status = -1;
                throw;
            }
            catch (OperationCanceledException ex) when (ex.CancellationToken == cancellationToken)
            {
                sw.Stop();
                procLog.Status = -1;

                await context.SqlLog.Error(ex, $"{config.TriggerType}执行超时被取消：" + ex.Message, true);
                throw new CustomException($"{config.TriggerType}执行超时：任务被取消");
            }
            catch (Exception ex)
            {
                sw.Stop();
                procLog.Status = -1;

                await context.SqlLog.Error(ex, $"{config.TriggerType}执行异常：" + ex.Message, true);
                throw;
            }
            finally
            {
                sw.Stop();
                procLog.EndTime = DateTime.Now;
                procLog.Duration = (int)sw.ElapsedMilliseconds;
                
                // 更新流量信息
                if (config.FinalTrafficCalculator != null)
                {
                    procLog.TotalTraffic += config.FinalTrafficCalculator();
                }

                if (config.LogSettings.Flow)
                    await context.SqlLog.Write(procLog, SqlType.Update);

                // 执行清理操作
                config.CleanupAction?.Invoke(context, result, procLog);
            }

            return result;
        }
    }

    /// <summary>
    /// 函数执行配置
    /// </summary>
    public class FunctionExecutionConfig
    {
        /// <summary>
        /// 跟踪ID，如果为空则自动生成
        /// </summary>
        public string TrackId { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public string Creator { get; set; } = "sys";

        /// <summary>
        /// 解决方案ID
        /// </summary>
        public string SolutionId { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; }

        /// <summary>
        /// 函数ID
        /// </summary>
        public string FunctionId { get; set; }

        /// <summary>
        /// 函数名称
        /// </summary>
        public string FunctionName { get; set; }

        /// <summary>
        /// 函数版本
        /// </summary>
        public long? Version { get; set; }

        /// <summary>
        /// 触发类型（API、JOB、EVENT等）
        /// </summary>
        public string TriggerType { get; set; }

        /// <summary>
        /// 函数参数
        /// </summary>
        public IDictionary<string, object> Args { get; set; }

        /// <summary>
        /// 全局数据
        /// </summary>
        public Dictionary<string, object> GlobalData { get; set; }

        /// <summary>
        /// 初始流量（KB）
        /// </summary>
        public decimal? InitialTraffic { get; set; }

        /// <summary>
        /// 最终流量计算器
        /// </summary>
        public Func<decimal> FinalTrafficCalculator { get; set; }

        /// <summary>
        /// 日志设置
        /// </summary>
        public FlowLogStatus LogSettings { get; set; }

        /// <summary>
        /// 清理操作
        /// </summary>
        public Action<FunctionContext, object, LcFruProc> CleanupAction { get; set; }
    }

    /// <summary>
    /// 函数执行配置构建器
    /// </summary>
    public static class FunctionExecutionConfigBuilder
    {
        /// <summary>
        /// 创建JOB执行配置
        /// </summary>
        public static FunctionExecutionConfig ForJob(
            string solutionId,
            string projectId,
            string functionId,
            string functionName,
            string creator,
            IDictionary<string, object> args = null,
            Dictionary<string, object> globalData = null,
            long? version = null)
        {
            return new FunctionExecutionConfig
            {
                SolutionId = solutionId,
                ProjectId = projectId,
                FunctionId = functionId,
                FunctionName = functionName,
                Creator = creator,
                Version = version,
                TriggerType = "JOB",
                Args = args,
                GlobalData = globalData,
                LogSettings = SqlLogSettings.JobStatus
            };
        }

        /// <summary>
        /// 创建API执行配置
        /// </summary>
        public static FunctionExecutionConfig ForApi(
            string solutionId,
            string projectId,
            string functionId,
            string functionName,
            IDictionary<string, object> args = null,
            Dictionary<string, object> globalData = null,
            decimal? initialTraffic = null,
            long? version = null)
        {
            return new FunctionExecutionConfig
            {
                SolutionId = solutionId,
                ProjectId = projectId,
                FunctionId = functionId,
                FunctionName = functionName,
                Creator = "sys",
                Version = version,
                TriggerType = "API",
                Args = args,
                GlobalData = globalData,
                InitialTraffic = initialTraffic,
                LogSettings = SqlLogSettings.ApiStatus
            };
        }

        /// <summary>
        /// 创建EVENT执行配置
        /// </summary>
        public static FunctionExecutionConfig ForEvent(
            string solutionId,
            string projectId,
            string functionId,
            string functionName,
            IDictionary<string, object> args = null,
            decimal? initialTraffic = null,
            long? version = null)
        {
            return new FunctionExecutionConfig
            {
                SolutionId = solutionId,
                ProjectId = projectId,
                FunctionId = functionId,
                FunctionName = functionName,
                Creator = "sys",
                Version = version,
                TriggerType = "EVENT",
                Args = args,
                InitialTraffic = initialTraffic,
                LogSettings = SqlLogSettings.MessageStatus
            };
        }
    }
}
