<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="数据源" prop="dataSource">
              <value-input
                v-model:data-value="formData.dataSource"
                :borderless="false"
                size="medium"
                only-variable
                :auto-hide-edit="false"
              ></value-input>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>
      <div class="form-body">
        <action-form-title title="输出参数"> </action-form-title>
        <variable-list v-model:data="forEachData.item" :on-change="onVariableChange"></variable-list>
      </div>
    </t-form>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ForEachActions',
};
</script>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { watch } from 'vue';

import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';
import VariableList from '@/components/action-panel/VariableList.vue';

import { useForEachStore } from './store';

const actionFlowStore = useActionFlowStore();
const dataStore = useForEachStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    dataStore.updateState();
  },
  {
    immediate: true,
  },
);
const { args: formData, forEachData } = storeToRefs(dataStore);

watch(
  () => formData.value,
  (newValue) => {
    dataStore.setArgs(newValue);
  },
  {
    deep: true,
  },
);

const onVariableChange = () => {
  dataStore.setForEachData();
};
</script>
<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}
</style>
