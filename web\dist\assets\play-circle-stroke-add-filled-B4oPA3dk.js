import{d as v,h as a,ab as y,ac as C,ad as O}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 1C18.0751 1 23 5.92489 23 12C23 12.9902 22.8692 13.9498 22.6239 14.8626C21.6561 14.0142 20.3881 13.5 19 13.5C15.9624 13.5 13.5 15.9624 13.5 19C13.5 20.3881 14.0142 21.6561 14.8626 22.6239C13.9499 22.8692 12.9902 23.0001 12 23.0001C5.92486 23.0001 1 18.0752 1 12C1 5.92489 5.92486 1 12 1ZM16.8028 11.9999L9.5 7.13135V16.8684L16.8028 11.9999Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M20 18V15H18V18H15V20H18V23H20V20H23V18H20Z"}}]},b=v({name:"PlayCircleStrokeAddFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=y(t),p=a(()=>["t-icon","t-icon-play-circle-stroke-add-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),d=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>C(g,d.value)}});export{b as default};
