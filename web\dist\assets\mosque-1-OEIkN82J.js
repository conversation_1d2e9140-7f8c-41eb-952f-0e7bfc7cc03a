import{d as O,h as a,ab as y,ac as V,ad as d}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 1.58594L10 5.58594V7.89656C10.1238 7.68585 10.2607 7.47803 10.4117 7.27618C11.1975 6.22567 12.3694 5.33801 14 5.07754V3.50015H16V5.07754C17.6306 5.33801 18.8025 6.22567 19.5883 7.27618C20.2464 8.15602 20.6374 9.14928 20.8403 10.0002H22V22.0002H2V5.58594L6 1.58594ZM18.7636 10.0002C18.5981 9.49514 18.3457 8.954 17.9867 8.47413C17.3664 7.64483 16.4379 7.00015 15 7.00015C13.5621 7.00015 12.6336 7.64483 12.0133 8.47413C11.6543 8.954 11.4019 9.49514 11.2364 10.0002H18.7636ZM10 12.0002V20.0002H12V14.0002H18V20.0002H20V12.0002H10ZM16 20.0002V16.0002H14V20.0002H16ZM8 20.0002V6.41436L6 4.41436L4 6.41436V20.0002H8ZM7.00391 8.00015V10.0041H5V8.00406H5.00391V8.00015H7.00391Z"}}]},H=O({name:"Mosque1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-mosque-1",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>V(m,v.value)}});export{H as default};
