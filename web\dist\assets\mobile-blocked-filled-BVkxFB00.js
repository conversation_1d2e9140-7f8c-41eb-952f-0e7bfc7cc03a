import{d as v,h as l,ab as C,ac as O,ad as b}from"./index-CmSBegZC.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){b(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 1H20V3.28988C19.3663 3.10128 18.695 3 18 3H6V17H18C18.695 17 19.3663 16.8987 20 16.7101V23H4V1ZM13.0039 19H11V21.0039H13.0039V19Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M18 4.5C14.9624 4.5 12.5 6.96243 12.5 10C12.5 11.5184 13.1167 12.8948 14.1108 13.889C15.105 14.8832 16.4814 15.5 18 15.5C21.0376 15.5 23.5 13.0376 23.5 10C23.5 8.48155 22.8833 7.10523 21.8892 6.11103C20.895 5.11675 19.5186 4.5 18 4.5ZM14.5 10C14.5 8.067 16.067 6.5 18 6.5C18.6032 6.5 19.1701 6.65208 19.6654 6.92057L14.9205 11.6652C14.652 11.17 14.5 10.6031 14.5 10ZM16.3346 13.0794L21.0795 8.33483C21.348 8.83 21.5 9.39687 21.5 10C21.5 11.933 19.933 13.5 18 13.5C17.3968 13.5 16.8299 13.3479 16.3346 13.0794Z"}}]},g=v({name:"MobileBlockedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=l(()=>e.size),{className:a,style:s}=C(r),p=l(()=>["t-icon","t-icon-mobile-blocked-filled",a.value]),u=l(()=>c(c({},s.value),t.style)),f=l(()=>({class:p.value,style:u.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>O(y,f.value)}});export{g as default};
