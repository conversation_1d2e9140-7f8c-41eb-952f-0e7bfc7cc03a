<template>
  <div class="condition-select">
    <div class="condition-line"></div>
    <div class="condition-type">
      <t-select v-model="conditionInfo.operator" :options="nodeOptions" size="small" :disabled="props.simple">
      </t-select>
    </div>
    <ul class="condition-list">
      <li v-for="(item, index) in conditionInfo.children" :key="item.id">
        <div v-if="item.type === 'leaf'" class="condition-leaf">
          <t-space>
            <t-tooltip v-if="openFilter" content="是否启用过滤模式（如果值为空，则会忽略该条件）">
              <t-checkbox v-model="item.isFilter" style="margin-top: 3px"></t-checkbox>
            </t-tooltip>

            <t-select
              v-if="columnsOptions && columnsOptions.length > 0"
              v-model="item.column"
              size="small"
              style="width: 160px"
              placeholder="请选择字段"
              clearable
              :filter="onFilterColumns"
            >
              <t-option v-for="option in columnsOptions" :key="option.value" :value="option.value">
                <t-space>
                  <div>{{ option.value }}</div>
                  <t-tag v-if="option.description" size="small">{{ option.description }}</t-tag>
                </t-space>
              </t-option>
            </t-select>
            <value-input v-else v-model:data-value="item.columnValue" style="width: 180px"></value-input>
            <t-select
              v-model="item.operator"
              style="width: 90px"
              :options="leafOptions"
              size="small"
              :disabled="props.simple"
            ></t-select>
            <value-input v-model:data-value="item.value" style="width: 150px"></value-input>
            <t-dropdown :options="addOptions" @click="(item) => onClickAdd(item.value, index)">
              <t-button
                size="small"
                shape="square"
                variant="text"
                style="color: var(--td-success-color-hover)"
                @click="() => onClickAdd(1, index)"
              >
                <template #icon><add-icon /></template>
              </t-button>
            </t-dropdown>
            <t-button
              v-if="conditionInfo.children?.length > 1"
              size="small"
              shape="square"
              variant="text"
              style="color: var(--td-error-color)"
              @click="onClickRemove(index)"
            >
              <template #icon><remove-icon /></template>
            </t-button>
          </t-space>
        </div>
        <ConditionSelect
          v-if="item.type === 'node'"
          :simple="props.simple"
          :data="item"
          :columns-options="columnsOptions"
          :open-filter="props.openFilter"
          @update:data="updateChild(index, $event)"
          @remove="() => onClickRemoveSub(index)"
        />
      </li>
    </ul>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ConditionSelect',
};
</script>
<script setup lang="ts">
import { AddIcon, RemoveIcon } from 'tdesign-icons-vue-next';
import { computed, ref, watch, watchEffect } from 'vue';

import ValueInput from '@/components/action-panel/ValueInput.vue';

import { ConditionInfo } from './model';
import { getRandomId } from './utils';

const nodeOptions = [
  { label: '并且', value: 'AND' },
  { label: '或', value: 'OR' },
];
const leafOptions = [
  { label: '等于', value: 'Equals' },
  { label: '不等于', value: 'NotEquals' },
  { label: '大于', value: 'GreaterThan' },
  { label: '小于', value: 'LessThan' },
  { label: '大于等于', value: 'GreaterThanOrEqual' },
  { label: '小于等于', value: 'LessThanOrEqual' },
  { label: '包含', value: 'In' },
  { label: '不包含', value: 'NotIn' },
  { label: '匹配', value: 'Like' },
  { label: '不匹配', value: 'NotLike' },
  { label: '左匹配', value: 'LeftLike' },
  { label: '右匹配', value: 'RightLike' },
  // { label: '存在', value: 'Exists' },
  // { label: '不存在', value: 'NotExists' },
];

const props = defineProps<{
  data: ConditionInfo | undefined;
  columnsOptions?: any[];
  simple?: boolean;
  openFilter?: boolean;
}>();

const addOptions = computed(() => {
  const options = [{ content: '添加相邻条件', value: 1 }];
  if (!props.simple) {
    options.push({ content: '添加子条件', value: 2 });
  }
  return options;
});

const conditionInfo = ref<ConditionInfo>(
  props.data || {
    id: 'root',
    type: 'node',
    operator: 'AND',
    children: [{ id: 'first', type: 'leaf', operator: 'Equals', isFilter: false }],
  },
);

watchEffect(() => {
  conditionInfo.value = props.data || {
    id: 'root',
    type: 'node',
    operator: 'AND',
    children: [{ id: 'first', type: 'leaf', operator: 'Equals', isFilter: false }],
  };
});

const emits = defineEmits(['update:data', 'remove']);
watch(
  conditionInfo,
  (newVal) => {
    emits('update:data', newVal);
  },
  { deep: true },
);

const updateChild = (index, data) => {
  conditionInfo.value.children[index] = data;
};

const onClickAdd = (item, index) => {
  let data;
  if (item === 1) {
    data = { id: getRandomId(), type: 'leaf', operator: 'Equals', isFilter: false };
  } else if (item === 2) {
    data = {
      id: getRandomId(),
      type: 'node',
      operator: 'AND',
      children: [{ id: getRandomId(), type: 'leaf', operator: 'Equals', isFilter: false }],
    };
  }
  conditionInfo.value.children.splice(index + 1, 0, data);
};

const onClickRemove = (index) => {
  conditionInfo.value.children.splice(index, 1);
  emits('remove', { index });
};

const onClickRemoveSub = (index) => {
  const subCondition = conditionInfo.value.children[index];
  if (subCondition.type === 'node' && subCondition.children.length === 0) {
    conditionInfo.value.children.splice(index, 1);
  }
};

const onFilterColumns = (search, option) => {
  const keyword = search.toLowerCase();
  const column = props.columnsOptions.find((t) => t.value === option.value);
  const result =
    option.value.indexOf(keyword) !== -1 ||
    (column && column.description && column.description.toLowerCase().indexOf(keyword) !== -1);
  return result;
};
</script>
<style lang="less" scoped>
.condition-select {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  position: relative;

  .condition-line {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 35px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 35px;
      width: 100%;
      height: 100%;
      border-top-left-radius: 50%;
      border-bottom-left-radius: 50%;
      border: 2px dashed #ccc;
      border-right: none;
    }
  }

  .condition-type {
    width: 70px;
    align-self: center;
    position: relative;
    top: 4px;
  }

  .condition-list {
    flex: 1;
    padding: 8px 0 0 16px;

    > li {
      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
