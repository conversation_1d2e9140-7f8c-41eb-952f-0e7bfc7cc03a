import{l as q,r as m,d as $,f as u,b as _,o as y,n as t,e as w,g as o,a as g,u as k,p as d,t as I,y as A,M as U,_ as B}from"./index-CmSBegZC.js";const M=(C=60)=>{let i;q(()=>{clearInterval(i)});const n=m(0);return[n,()=>{n.value=C,i=setInterval(()=>{n.value>0?n.value-=1:(clearInterval(i),n.value=0)},1e3)}]},L={class:"switch-container"},P=$({__name:"Register",emits:["registerSuccess"],setup(C,{emit:i}){const n={phone:"",email:"",password:"",verifyCode:"",checked:!1},h={phone:[{required:!0,message:"手机号必填",type:"error"}],email:[{required:!0,message:"邮箱必填",type:"error"},{email:!0,message:"请输入正确的邮箱",type:"warning"}],password:[{required:!0,message:"密码必填",type:"error"}],verifyCode:[{required:!0,message:"验证码必填",type:"error"}]},s=m("phone"),b=m(),a=m({...n}),p=m(!1),[V,z]=M(),D=i,N=f=>{if(f.validateResult===!0){if(!a.value.checked){U.error("请同意服务协议和隐私声明");return}U.success("注册成功"),D("registerSuccess")}},R=f=>{b.value.reset(),s.value=f};return(f,e)=>{const c=u("t-icon"),v=u("t-input"),r=u("t-form-item"),x=u("t-button"),S=u("t-checkbox"),T=u("t-form");return y(),_(T,{ref_key:"form",ref:b,class:A(["item-container",`register-${s.value}`]),data:a.value,rules:h,"label-width":"0",onSubmit:N},{default:t(()=>[s.value=="phone"?(y(),_(r,{key:0,name:"phone"},{default:t(()=>[o(v,{modelValue:a.value.phone,"onUpdate:modelValue":e[0]||(e[0]=l=>a.value.phone=l),maxlength:11,size:"large",placeholder:"请输入您的手机号"},{"prefix-icon":t(()=>[o(c,{name:"user"})]),_:1},8,["modelValue"])]),_:1})):w("",!0),s.value=="email"?(y(),_(r,{key:1,name:"email"},{default:t(()=>[o(v,{modelValue:a.value.email,"onUpdate:modelValue":e[1]||(e[1]=l=>a.value.email=l),type:"text",size:"large",placeholder:"请输入您的邮箱"},{"prefix-icon":t(()=>[o(c,{name:"mail"})]),_:1},8,["modelValue"])]),_:1})):w("",!0),o(r,{name:"password"},{default:t(()=>[o(v,{modelValue:a.value.password,"onUpdate:modelValue":e[3]||(e[3]=l=>a.value.password=l),size:"large",type:p.value?"text":"password",clearable:"",placeholder:"请输入登录密码"},{"prefix-icon":t(()=>[o(c,{name:"lock-on"})]),"suffix-icon":t(()=>[o(c,{name:p.value?"browse":"browse-off",onClick:e[2]||(e[2]=l=>p.value=!p.value)},null,8,["name"])]),_:1},8,["modelValue","type"])]),_:1}),s.value=="phone"?(y(),_(r,{key:2,class:"verification-code",name:"verifyCode"},{default:t(()=>[o(v,{modelValue:a.value.verifyCode,"onUpdate:modelValue":e[4]||(e[4]=l=>a.value.verifyCode=l),size:"large",placeholder:"请输入验证码"},null,8,["modelValue"]),o(x,{variant:"outline",disabled:k(V)>0,onClick:k(z)},{default:t(()=>[d(I(k(V)==0?"发送验证码":`${k(V)}秒后可重发`),1)]),_:1},8,["disabled","onClick"])]),_:1})):w("",!0),o(r,{class:"check-container",name:"checked"},{default:t(()=>[o(S,{modelValue:a.value.checked,"onUpdate:modelValue":e[5]||(e[5]=l=>a.value.checked=l)},{default:t(()=>e[7]||(e[7]=[d("我已阅读并同意 ",-1)])),_:1,__:[7]},8,["modelValue"]),e[8]||(e[8]=d()),e[9]||(e[9]=g("span",null,"服务协议",-1)),e[10]||(e[10]=d(" 和 ",-1)),e[11]||(e[11]=g("span",null,"隐私声明",-1))]),_:1,__:[8,9,10,11]}),o(r,null,{default:t(()=>[o(x,{block:"",size:"large",type:"submit"},{default:t(()=>e[12]||(e[12]=[d(" 注册 ",-1)])),_:1,__:[12]})]),_:1}),g("div",L,[g("span",{class:"tip",onClick:e[6]||(e[6]=l=>R(s.value=="phone"?"email":"phone"))},I(s.value=="phone"?"使用邮箱注册":"使用手机号注册"),1)])]),_:1},8,["class","data"])}}}),F=B(P,[["__scopeId","data-v-f73e8ca9"]]);export{F as default};
