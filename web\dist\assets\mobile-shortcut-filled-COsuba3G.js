import{d,h as a,ab as O,ac as y,ad as b}from"./index-CmSBegZC.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){b(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4 1H20V5H18V3H6V17H20V23H4V1ZM13.0039 19H11V21.0039H13.0039V19Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M23.4551 14.506L21.8863 15.2869 21.1054 16.8557 20.3245 15.2869 18.7557 14.506 20.3245 13.7251 21.1054 12.1562 21.8863 13.7251 23.4551 14.506zM14.9599 12.0966L16.057 14.3007 17.1541 12.0966 19.3581 10.9995 17.1541 9.90243 16.057 7.69839 14.9599 9.90243 12.7559 10.9995 14.9599 12.0966zM23.4551 7.4962L21.8863 8.27709 21.1054 9.84589 20.3245 8.27709 18.7557 7.4962 20.3245 6.71529 21.1054 5.14648 21.8863 6.71529 23.4551 7.4962z"}}]},g=d({name:"MobileShortcutFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=O(r),p=a(()=>["t-icon","t-icon-mobile-shortcut-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{g as default};
