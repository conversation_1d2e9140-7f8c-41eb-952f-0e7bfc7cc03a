import{d,h as a,ab as O,ac as m,ad as y}from"./index-CmSBegZC.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function p(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.8329 1.94533L11.2782 1.11328 9.61407 2.22268 10.1688 3.05473C10.4024 3.40512 10.5704 3.75457 10.6913 4.11077 7.98971 4.57048 7.59344 7.12948 7.43418 9.62635 7.31763 11.4535 7.47432 13.4155 7.77275 15.238 8.07111 17.06 8.50848 18.7266 8.94452 19.9595 9.5476 21.6648 10.1562 22 12.0047 22 13.9722 22 14.4246 21.5632 15.0527 19.7515 15.4893 18.4921 15.926 16.8395 16.2236 15.0493 16.5215 13.2581 16.6778 11.3437 16.5619 9.55657 16.3777 6.71622 15.803 4.32644 12.7492 4.03536 12.581 3.3501 12.3005 2.64682 11.8329 1.94533zM6.28719 5.28005C6.32369 5.19938 6.36109 5.12052 6.39965 5.04324 6.02307 5.03328 5.63557 5.06611 5.25791 5.144 4.07803 5.38733 2.8296 6.10124 2.10511 7.55345 1.64087 8.484 1.50554 9.79303 1.5364 11.0989 1.56817 12.4429 1.781 13.9357 2.13039 15.331 2.47767 16.7179 2.97439 18.0667 3.60467 19.0944 4.19141 20.051 5.10169 21.0587 6.37036 21.0587H7.22164C7.16301 20.9131 7.10924 20.7684 7.05896 20.6262 6.57961 19.2708 6.11461 17.4883 5.79903 15.5611 5.48366 13.6351 5.30954 11.5165 5.43823 9.49891 5.51745 8.25693 5.66394 6.65754 6.28719 5.28005zM16.9423 20.4065C16.8706 20.6134 16.7923 20.8334 16.7018 21.0587H17.1696C18.4925 21.0587 19.4923 20.1377 20.1779 19.1367 20.8869 18.1017 21.4327 16.743 21.811 15.3502 22.1917 13.9487 22.4194 12.4512 22.4582 11.104 22.4959 9.79403 22.3602 8.48466 21.8956 7.55346 21.1716 6.10223 19.9262 5.38773 18.7477 5.1441 18.3591 5.06378 17.9603 5.03134 17.5737 5.04426 18.28 6.42097 18.4682 8.047 18.5577 9.427 18.6862 11.4084 18.5116 13.4822 18.1965 15.3772 17.881 17.2748 17.4171 19.0369 16.9423 20.4065z"}}]},C=d({name:"PumpkinFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=O(t),c=a(()=>["t-icon","t-icon-pumpkin-filled",l.value]),u=a(()=>p(p({},s.value),r.style)),f=a(()=>({class:c.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>m(b,f.value)}});export{C as default};
