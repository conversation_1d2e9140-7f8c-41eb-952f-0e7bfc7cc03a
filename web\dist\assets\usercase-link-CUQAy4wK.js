import{d as f,h as a,ab as O,ac as y,ad as d}from"./index-CmSBegZC.js";function s(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function C(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?s(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 11C5.38585 11 4.82189 11.1588 4.43338 11.4178C4.05295 11.6715 4 11.9015 4 12C4 12.0985 4.05295 12.3285 4.43338 12.5822C4.82189 12.8412 5.38585 13 6 13C6.61415 13 7.17811 12.8412 7.56662 12.5822C7.94705 12.3285 8 12.0985 8 12C8 11.9015 7.94705 11.6715 7.56662 11.4178C7.17811 11.1588 6.61415 11 6 11ZM6 9C6.92138 9 7.89295 9.23169 8.67602 9.75374C9.46716 10.2812 10 11.0748 10 12C10 12.9252 9.46716 13.7188 8.67602 14.2463C7.89295 14.7683 6.92138 15 6 15C5.07862 15 4.10705 14.7683 3.32398 14.2463C2.53284 13.7188 2 12.9252 2 12C2 11.0748 2.53284 10.2812 3.32398 9.75374C4.10705 9.23169 5.07862 9 6 9ZM18 11C17.3858 11 16.8219 11.1588 16.4334 11.4178C16.0529 11.6715 16 11.9015 16 12C16 12.0985 16.0529 12.3285 16.4334 12.5822C16.8219 12.8412 17.3858 13 18 13C18.6142 13 19.1781 12.8412 19.5666 12.5822C19.9471 12.3285 20 12.0985 20 12C20 11.9015 19.9471 11.6715 19.5666 11.4178C19.1781 11.1588 18.6142 11 18 11ZM18 9C18.9214 9 19.893 9.23169 20.676 9.75374C21.4672 10.2812 22 11.0748 22 12C22 12.9252 21.4672 13.7188 20.676 14.2463C19.893 14.7683 18.9214 15 18 15C17.0786 15 16.107 14.7683 15.324 14.2463C14.5328 13.7188 14 12.9252 14 12C14 11.0748 14.5328 10.2812 15.324 9.75374C16.107 9.23169 17.0786 9 18 9ZM10.998 10.9961H13.002V13H10.998V10.9961Z"}}]},g=f({name:"UsercaseLinkIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:l}=O(t),c=a(()=>["t-icon","t-icon-usercase-link",o.value]),p=a(()=>C(C({},l.value),r.style)),u=a(()=>({class:c.value,style:p.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(m,u.value)}});export{g as default};
