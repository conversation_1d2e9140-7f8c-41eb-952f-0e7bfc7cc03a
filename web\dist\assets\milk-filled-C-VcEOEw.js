import{d,h as a,ab as O,ac as y,ad as m}from"./index-CmSBegZC.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6.15 1H17.85V4.59026L20 9.49026V23H4V9.49026L6.15 4.59026V1ZM18 9.90974L16.9322 7.47622L16.05 9.87786V21H18V9.90974ZM7.80325 5.8L6.5308 8.7H14.352L15.4173 5.8H7.80325ZM8.15 3V3.8H15.85V3H8.15ZM13 13H11.5743L9.9984 14.5838L8.41536 13H7V19H9V16.4141L10.0016 17.4162L11 16.4127V19H13V13Z"}}]},V=d({name:"MilkFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-milk-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(b,f.value)}});export{V as default};
