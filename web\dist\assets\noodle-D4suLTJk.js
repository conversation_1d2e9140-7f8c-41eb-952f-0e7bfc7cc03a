import{d as C,h as a,ab as d,ac as O,ad as y}from"./index-CmSBegZC.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15 3L22 3V5H21V10L22 10V12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12V10L3 10C3 6.68629 5.68629 4 9 4C12.3137 4 15 6.68629 15 10L16 10V5L15 5L15 3ZM18 5V10H18.9995L19 5H18ZM4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12H4ZM5 10L6 10C6 8.34315 7.34315 7 9 7C10.6569 7 12 8.34315 12 10L13 10C13 7.79086 11.2091 6 9 6C6.79086 6 5 7.79086 5 10ZM10 10C10 9.44772 9.55228 9 9 9C8.44772 9 8 9.44772 8 10H10Z"}}]},g=C({name:"NoodleIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=d(t),p=a(()=>["t-icon","t-icon-noodle",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(m,v.value)}});export{g as default};
