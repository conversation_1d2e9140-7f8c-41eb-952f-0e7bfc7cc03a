import{d,h as a,ab as O,ac as y,ad as m}from"./index-CmSBegZC.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8.64494 20.5C9.07521 21.9457 10.4145 23 12 23 13.5855 23 14.9248 21.9457 15.3551 20.5H8.64494zM3 19.5H21V16.5L19 13.5V8.5C19 4.63401 15.866 1.5 12 1.5 8.13401 1.5 5 4.63401 5 8.5V13.5L3 16.5V19.5z"}}]},P=d({name:"NotificationFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:i,style:s}=O(r),p=a(()=>["t-icon","t-icon-notification-filled",i.value]),f=a(()=>c(c({},s.value),t.style)),u=a(()=>({class:p.value,style:f.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(b,u.value)}});export{P as default};
