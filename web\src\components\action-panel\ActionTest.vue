<template>
  <CmpContainer full>
    <CmpCard>
      <div class="test-panel">
        <!-- 测试参数区域 -->
        <div class="test-input-section">
          <div class="section-header">
            <h3>测试参数</h3>
            <t-button theme="primary" size="small" @click="generateTestData"> 根据基础信息生成 </t-button>
          </div>
          <div class="json-editor-container">
            <vue-monaco-editor
              v-model:value="testParams"
              language="json"
              :options="editorOptions"
              height="300px"
              @mount="onEditorMount"
            />
          </div>
        </div>

        <!-- 执行配置 -->
        <div class="test-config">
          <div class="section-header">
            <h3>执行配置</h3>
          </div>
          <t-form layout="inline">
            <t-form-item label="触发类型">
              <t-select v-model="triggerType" style="width: 120px">
                <t-option value="TEST" label="测试" />
                <t-option value="API" label="API" />
                <t-option value="JOB" label="任务" />
                <t-option value="EVENT" label="事件" />
              </t-select>
            </t-form-item>
          </t-form>
        </div>

        <!-- 执行按钮 -->
        <div class="test-actions">
          <t-button theme="primary" size="large" @click="executeTest" :loading="executing"> 执行测试 </t-button>
        </div>

        <!-- 执行日志区域 -->
        <div class="test-result-section">
          <div class="section-header">
            <h3>执行日志</h3>
            <t-button theme="default" size="small" @click="clearLogs"> 清空日志 </t-button>
          </div>
          <div class="logs-container">
            <logs-panel
              v-if="logData"
              :process-id="logData.processId"
              :process-name="logData.processName || '测试执行'"
              :is-run="false"
            />
            <div v-else class="empty-logs">
              <t-empty description="暂无执行日志" />
            </div>
          </div>
        </div>
      </div>
    </CmpCard>
  </CmpContainer>
</template>

<script lang="ts">
export default {
  name: 'ActionTest',
};
</script>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { VueMonacoEditor } from '@guolao/vue-monaco-editor';
import { api, Services } from '@/api/system';
import { FlowInfo } from './model';
import LogsPanel from './LogsPanel.vue';
import dayjs from 'dayjs';

const props = defineProps<{
  functionId: string;
  flowInfo: FlowInfo;
}>();

// Monaco Editor 配置
const editorOptions = {
  automaticLayout: true,
  scrollBeyondLastLine: false,
  minimap: { enabled: false },
  wordWrap: 'on' as const,
  formatOnPaste: true,
  formatOnType: true,
  tabSize: 2,
  insertSpaces: true,
};

// 测试参数
const testParams = ref('{}');
const executing = ref(false);
const logData = ref(null);
const triggerType = ref('TEST');

// 编辑器实例
let editorInstance: any = null;

const onEditorMount = (editor: any) => {
  editorInstance = editor;
  // 格式化初始内容
  setTimeout(() => {
    editor.getAction('editor.action.formatDocument')?.run();
  }, 100);
};

// 根据基础信息生成测试数据
const generateTestData = () => {
  try {
    let testData: any = {};

    // 从流程信息中提取输入参数
    if (props.flowInfo?.data) {
      // 调试：打印所有变量信息
      console.log('所有变量信息:', props.flowInfo.data);

      // 查找输入变量，包括 input 类型和启动变量
      const inputVariables = props.flowInfo.data;

      console.log('筛选出的输入变量:', inputVariables);

      if (inputVariables.length > 0) {
        // 创建虚拟根节点来包含所有输入变量
        const virtualRoot = {
          id: 'VIRTUAL_ROOT',
          key: 'ROOT',
          type: 'object',
          children: inputVariables,
        };

        // 使用 VariableBatchDialog 的 flowDataToJson 逻辑
        testData = flowDataToJson(virtualRoot);
        console.log('生成的测试数据:', testData);
      }
    }

    // 如果没有找到输入参数，生成一个基本的示例
    if (Object.keys(testData).length === 0) {
      testData.example = '请根据实际需要修改测试参数';
    }

    testParams.value = JSON.stringify(testData, null, 2);

    // 格式化编辑器内容
    setTimeout(() => {
      editorInstance?.getAction('editor.action.formatDocument')?.run();
    }, 100);

    MessagePlugin.success('已生成测试参数');
  } catch (error) {
    console.error('生成测试数据失败:', error);
    MessagePlugin.error('生成测试数据失败');
  }
};

// 参考 VariableBatchDialog 的 flowDataToJson 逻辑
const flowDataToJson = (flowData: any): any => {
  if (!flowData) return null;

  if (flowData.type === 'object') {
    const objectResult: any = {};
    for (const child of flowData.children || []) {
      const key = child.key?.toString().trim();
      if (key) {
        objectResult[key] = flowDataToJson(child);
      }
    }
    return objectResult;
  }

  if (flowData.type === 'array') {
    const arrayResult: any[] = [];
    const objectResult: any = {};
    for (const child of flowData.children || []) {
      const key = child.key?.toString().trim();
      if (key) {
        objectResult[key] = flowDataToJson(child);
      }
    }
    arrayResult.push(objectResult);
    return arrayResult;
  }

  if (flowData.value && flowData.value.type === 'text') {
    return flowData.value.textValue;
  }

  // 获取数据类型，优先使用 value.dataType，然后是 type，最后检查特殊情况
  let dataType = flowData.value?.dataType || flowData.type;

  // // 特殊处理：如果是启动变量且没有明确类型，根据变量名推断
  // if (!dataType && flowData.key) {
  //   const keyLower = flowData.key.toLowerCase();
  //   if (keyLower.includes('time') || keyLower.includes('date')) {
  //     dataType = 'DateTime';
  //   }
  // }

  // console.log(`变量 ${flowData.key} 的数据类型:`, dataType, '原始数据:', flowData);

  return typeToDefault(dataType);
};

// 参考 VariableBatchDialog 的 typeToDefault 逻辑
const typeToDefault = (type: string): any => {
  switch (type) {
    case 'object':
      return {};
    case 'array':
      return [];
    case 'char':
    case 'string':
      return '示例文本';
    case 'bool':
    case 'boolean':
      return true;
    case 'decimal':
    case 'int':
    case 'short':
    case 'long':
    case 'double':
    case 'float':
    case 'number':
      return 123;
    case 'Date':
      return dayjs().format('HH:mm:ss');
    case 'DateTime':
      return dayjs().format('YYYY-MM-DD HH:mm:ss');
    default:
      return null;
  }
};

// 执行测试
const executeTest = async () => {
  if (!props.functionId) {
    MessagePlugin.warning('请先选择要测试的函数');
    return;
  }

  try {
    // 验证 JSON 格式
    const params = JSON.parse(testParams.value);

    executing.value = true;

    // 调用函数测试接口进行测试
    const result = await api.run(Services.functionTest, {
      functionId: props.functionId,
      parameters: params,
      triggerType: triggerType.value,
    });

    // 设置日志数据，使用返回的 processId
    logData.value = {
      processId: result.processId,
      processName: '函数测试',
    };

    MessagePlugin.success('测试执行完成');
  } catch (error) {
    console.error('测试执行失败:', error);
    if (error.name === 'SyntaxError') {
      MessagePlugin.error('测试参数 JSON 格式错误，请检查语法');
    } else {
      MessagePlugin.error('测试执行失败：' + (error?.message || '未知错误'));
    }
  } finally {
    executing.value = false;
  }
};

// 清空日志
const clearLogs = () => {
  logData.value = null;
  MessagePlugin.success('已清空日志');
};

// 监听 functionId 变化，重置状态
watch(
  () => props.functionId,
  () => {
    testParams.value = '{}';
    logData.value = null;
  },
);
</script>

<style lang="less" scoped>
.test-panel {
  padding: 16px;
}

.test-input-section,
.test-config,
.test-result-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.json-editor-container {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  overflow: hidden;
}

.test-actions {
  text-align: center;
  margin: 24px 0;
}

.logs-container {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  overflow: hidden;
  height: 500px;
}

.empty-logs {
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
