import S from"./more-BPblJiA7.js";import{d as x,f as c,b as d,o as s,n as e,g as a,u as n,p as m,t as u,z as B,a as l,e as r,aP as N,_ as V}from"./index-CmSBegZC.js";import w from"./shop-BaF7KN5O.js";import D from"./service-C5vSmS1J.js";import P from"./user-avatar-CY01VPl6.js";import z from"./laptop-v6PTNopD.js";const A={class:"list-card-item_detail--name"},M={class:"list-card-item_detail--desc"},j=x({__name:"index",props:{product:{type:Object}},emits:["manage-product","delete-item"],setup(t,{emit:f}){const p=f,g=["A","B","C","D","E"],y=o=>{p("manage-product",o)},h=o=>{p("delete-item",o)};return(o,q)=>{const i=c("t-avatar"),k=c("t-tag"),v=c("t-avatar-group"),_=c("t-button"),b=c("t-dropdown"),C=c("t-card");return s(),d(C,{theme:"poster2",bordered:!1},{avatar:e(()=>[a(i,{size:"56px"},{icon:e(()=>[t.product.type===1?(s(),d(n(w),{key:0})):r("",!0),t.product.type===2?(s(),d(n(N),{key:1})):r("",!0),t.product.type===3?(s(),d(n(D),{key:2})):r("",!0),t.product.type===4?(s(),d(n(P),{key:3})):r("",!0),t.product.type===5?(s(),d(n(z),{key:4})):r("",!0)]),_:1})]),status:e(()=>[a(k,{theme:t.product.isSetup?"success":"default",disabled:!t.product.isSetup},{default:e(()=>[m(u(t.product.isSetup?o.t("components.isSetup.on"):o.t("components.isSetup.off")),1)]),_:1},8,["theme","disabled"])]),content:e(()=>[l("p",A,u(t.product.name),1),l("p",M,u(t.product.description),1)]),footer:e(()=>[a(v,{cascading:"left-up",max:2},{default:e(()=>[a(i,null,{default:e(()=>[m(u(g[t.product.type-1]),1)]),_:1}),a(i,null,{icon:e(()=>[a(n(B))]),_:1})]),_:1})]),actions:e(()=>[a(b,{disabled:!t.product.isSetup,trigger:"click",options:[{content:o.t("components.manage"),value:"manage",onClick:()=>y(t.product)},{content:o.t("components.delete"),value:"delete",onClick:()=>h(t.product)}]},{default:e(()=>[a(_,{theme:"default",disabled:!t.product.isSetup,shape:"square",variant:"text"},{default:e(()=>[a(n(S))]),_:1},8,["disabled"])]),_:1},8,["disabled","options"])]),_:1})}}}),H=V(j,[["__scopeId","data-v-4ecace77"]]);export{H as P};
